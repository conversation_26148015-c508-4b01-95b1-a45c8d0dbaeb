import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import {PackageTrackingService} from "../app/services/shippo/package_tracking_service.js";
import queue from "@rlanz/bull-queue/services/main";
import UpdatePackageTrackingHistoryJob from "#jobs/update_package_tracking_history_job";
import ZnOrderFulfillment from "#models/zn_order_fulfillment";

export default class UpdateVendorOrders extends BaseCommand {
  static commandName = 'update:vendor-orders'
  static description = ''

  static options: CommandOptions = {
    startApp: true
  }

  async run() {
    const packageTrackingService = new PackageTrackingService()
    const orders = await ZnOrderFulfillment.query()
      .whereNotNull('trackingCompany')
      .whereNotNull('trackingNumber');

    for (const order of orders) {
      try {
        const trackingNumber = order?.trackingNumber
        if (!trackingNumber) continue

        const trackingData = await packageTrackingService.getTrackings(order.trackingCompany, trackingNumber)
        if (!trackingData) continue

        // Update history and fields
        await queue.dispatch(UpdatePackageTrackingHistoryJob, { tracking: trackingData, from: 'api' }, { queueName: 'tracking' })

      } catch (error) {
        console.error(`Failed to update tracking for order ID ${order.id}:`, error)
      }
    }
  }
}
