# Admin Cart Edit Item API Documentation

## Overview

Các API mới cho admin để edit cart item với khả năng chỉnh sửa toàn bộ (section, item, số lượng). Logic được đảm bảo giống 100% với API hiện tại trong folder `/app`.

## Base URL

```
/v1/admin/carts
```

## Authentication

Tất cả endpoints yêu cầu JWT admin authentication:

```
Authorization: Bearer <admin_jwt_token>
```

## Endpoints

### 1. Add Item to Cart

**Endpoint:** `POST /v1/admin/carts/:cartId/items`

**Description:** Thêm product item vào cart - logic giống app API 100%

**Path Parameters:**
- `cartId`: Cart ID (string, UUID)

**Request Body:**
```json
{
  "variantId": "uuid",
  "quantity": 2,
  "affiliateId": "uuid", // Optional
  "notes": "Admin modification notes" // Optional
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "section-uuid",
    "cartId": "cart-uuid",
    "quantity": 2,
    "total": 59.98,
    "rawTotal": 59.98,
    "price": 29.99,
    "rawPrice": 29.99,
    "cartItems": [
      {
        "id": "item-uuid",
        "cartSectionId": "section-uuid",
        "productId": "product-uuid",
        "variantId": "variant-uuid",
        "quantity": 2,
        "price": 29.99,
        "productName": "Product Name",
        "variantName": "Variant Name"
      }
    ]
  },
  "message": "Item added to cart successfully"
}
```

### 2. Add Bundle to Cart

**Endpoint:** `POST /v1/admin/carts/:cartId/bundles`

**Description:** Thêm bundle vào cart - logic giống app API 100%

**Path Parameters:**
- `cartId`: Cart ID (string, UUID)

**Request Body:**
```json
{
  "bundleId": "uuid",
  "quantity": 1,
  "discountId": "uuid", // Optional
  "affiliateId": "uuid", // Optional
  "notes": "Admin modification notes", // Optional
  "items": [ // Optional - for product bundles
    {
      "variantId": "uuid",
      "itemId": "string"
    }
  ],
  "collections": [ // Optional - for collection bundles
    {
      "id": "uuid",
      "items": [
        {
          "id": "string",
          "variants": [
            {
              "id": "uuid",
              "quantity": 1
            }
          ]
        }
      ]
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "section-uuid",
    "cartId": "cart-uuid",
    "bundleId": "bundle-uuid",
    "quantity": 1,
    "total": 89.99,
    "title": "Bundle Name",
    "cartItems": [...]
  },
  "message": "Bundle added to cart successfully"
}
```

### 3. Update Section Quantity

**Endpoint:** `PUT /v1/admin/carts/sections/:cartSectionId/quantity`

**Description:** Cập nhật số lượng của cart section - logic giống app API 100%

**Path Parameters:**
- `cartSectionId`: Cart Section ID (string, UUID)

**Request Body:**
```json
{
  "quantity": 5, // Set to 0 to remove section
  "notes": "Admin modification notes" // Optional
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "section-uuid",
    "quantity": 5,
    "total": 149.95,
    "cartItems": [...]
  },
  "message": "Section quantity updated successfully"
}
```

**Note:** Nếu quantity = 0, section sẽ bị xóa và response.data sẽ là null.

### 4. Update Cart Item

**Endpoint:** `PUT /v1/admin/carts/items/:cartItemId`

**Description:** Cập nhật individual cart item properties

**Path Parameters:**
- `cartItemId`: Cart Item ID (string, UUID)

**Request Body:**
```json
{
  "quantity": 3, // Optional
  "price": 89.99, // Optional - admin can override price
  "notes": "Admin modification notes" // Optional
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "item-uuid",
    "quantity": 3,
    "price": 89.99,
    "product": {...},
    "variant": {...}
  },
  "message": "Cart item updated successfully"
}
```

### 5. Delete Cart Section

**Endpoint:** `DELETE /v1/admin/carts/sections/:cartSectionId`

**Description:** Xóa cart section - logic giống app API 100%

**Path Parameters:**
- `cartSectionId`: Cart Section ID (string, UUID)

**Request Body:**
```json
{
  "notes": "Admin modification notes" // Optional
}
```

**Response:**
```json
{
  "success": true,
  "message": "Cart section deleted successfully"
}
```

## Logic Consistency với App APIs

### 1. Item Merging
- Khi thêm item đã tồn tại, quantity sẽ được cộng dồn
- Logic merge giống 100% với app API

### 2. Bundle Handling
- Bundle merging behavior giống app API
- Discount calculation giống app API
- Bundle type handling (product/collection) giống app API

### 3. Price Calculation
- Sử dụng ShopCartService để đảm bảo logic pricing giống 100%
- Bundle discount application giống app API
- Raw price và final price calculation giống app API

### 4. Section Management
- Section creation/update/delete logic giống app API
- Quantity = 0 sẽ xóa section như app API

## Error Handling

### Validation Errors (422)
```json
{
  "errors": [
    {
      "field": "variantId",
      "message": "The variantId field must be a valid UUID"
    }
  ]
}
```

### Authorization Errors (401/403)
```json
{
  "success": false,
  "message": "Authentication required"
}
```

### Business Logic Errors (400)
```json
{
  "success": false,
  "message": "Variant not available for sale",
  "error": "Detailed error message"
}
```

### Not Found Errors (404)
```json
{
  "success": false,
  "message": "Cart section not found"
}
```

## Admin Audit Trail

Tất cả admin actions được log với:
- Admin ID
- Action type
- Target resource
- Modification notes (nếu có)
- Timestamp

## Testing

Sử dụng test file: `tests/admin/cart/admin_cart_api.test.ts`

```bash
node ace test tests/admin/cart/admin_cart_api.test.ts
```

## Security

- Yêu cầu admin authentication
- Authorization check với bouncer
- Input validation với Vine validators
- SQL injection protection với Lucid ORM
