import ZnAffiliate from '#models/zn_affiliate'
import { AffiliationService } from '#services/affiliation/affiliation_service';
import logger from '@adonisjs/core/services/logger';
import { Job } from '@rlanz/bull-queue'

interface CaptureAffiliateSnapshotsPayload { }

export default class CaptureAffiliateSnapshotsJob extends Job {
  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle({ }: CaptureAffiliateSnapshotsPayload) {
    const affiliateService = new AffiliationService();
    const affiliates = await ZnAffiliate.query()
      .where('registerStatus', 'approved');
    const date = new Date();

    for (const affiliate of affiliates) {
      await affiliateService.updateSnapshots(affiliate.id, date);
    }
    logger.info(`Snapshots updated for all affiliates`);
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: CaptureAffiliateSnapshotsPayload) { }
}
