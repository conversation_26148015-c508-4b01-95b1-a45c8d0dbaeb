# Admin Cart Management API Documentation

## Overview

The Admin Cart Management API provides comprehensive cart manipulation capabilities for administrators, allowing them to manage user shopping carts with full audit trail tracking. This API mirrors 100% of the app-side cart logic while adding administrative controls and audit capabilities.

## Base URL

```
https://your-domain.com/v1/admin/carts
```

## Authentication

All endpoints require JWT authentication with admin privileges. Include the following header:

```
Authorization: Bearer <admin_jwt_token>
```

## API Endpoints Overview

| Method   | Endpoint                                           | Purpose                       | Permission Required |
| -------- | -------------------------------------------------- | ----------------------------- | ------------------- |
| `GET`    | `/v1/admin/carts`                                  | List all carts with filtering | `READ` on `CART`    |
| `GET`    | `/v1/admin/carts/user/:userId`                     | Get or create user cart       | `READ` on `CART`    |
| `GET`    | `/v1/admin/carts/:id`                              | Get specific cart details     | `READ` on `CART`    |
| `DELETE` | `/v1/admin/carts/:id`                              | Delete entire cart            | `DELETE` on `CART`  |
| `POST`   | `/v1/admin/carts/:cartId/items`                    | Add product to cart           | `CREATE` on `CART`  |
| `POST`   | `/v1/admin/carts/:cartId/bundles`                  | Add bundle to cart            | `CREATE` on `CART`  |
| `PUT`    | `/v1/admin/carts/sections/:cartSectionId/quantity` | Update section quantity       | `UPDATE` on `CART`  |
| `DELETE` | `/v1/admin/carts/sections/:cartSectionId`          | Remove cart section           | `DELETE` on `CART`  |
| `PUT`    | `/v1/admin/carts/items/:cartItemId`                | Modify individual item        | `UPDATE` on `CART`  |
| `DELETE` | `/v1/admin/carts/sections/:cartSectionId/bundle`   | Remove bundle from section    | `UPDATE` on `CART`  |

---

## 1. List All Carts

**Endpoint:** `GET /v1/admin/carts`

**Purpose:** Retrieve all carts with pagination and advanced filtering

**Query Parameters:**

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `search` (optional): Search by user email/name
- `status` (optional): Filter by cart status
- `userId` (optional): Filter by specific user ID
- `dateFrom` (optional): Filter carts created after date
- `dateTo` (optional): Filter carts created before date

**Example Request:**

```bash
GET /v1/admin/carts?page=1&limit=20&search=john&status=active
```

**Response:**

```json
{
  "data": [
    {
      "id": "cart-uuid-1",
      "userId": "user-uuid-1",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T14:20:00Z",
      "user": {
        "id": "user-uuid-1",
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe"
      },
      "cartSections": [...],
      "itemCount": 5,
      "totalValue": 299.99,
      "lastActivity": "2024-01-15T14:20:00Z"
    }
  ],
  "meta": {
    "total": 150,
    "perPage": 20,
    "currentPage": 1,
    "lastPage": 8,
    "firstPage": 1,
    "hasMorePages": true
  }
}
```

---

## 2. Get or Create User Cart

**Endpoint:** `GET /v1/admin/carts/user/:userId`

**Purpose:** Retrieve existing cart for a user or create a new one if none exists

**Path Parameters:**

- `userId`: User's unique identifier

**Example Request:**

```bash
GET /v1/admin/carts/user/user-uuid-123
```

**Response:**

```json
{
  "id": "cart-uuid-456",
  "userId": "user-uuid-123",
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T14:20:00Z",
  "user": {
    "id": "user-uuid-123",
    "email": "<EMAIL>",
    "firstName": "Jane",
    "lastName": "Smith"
  },
  "cartSections": [
    {
      "id": "section-uuid-789",
      "section": "product",
      "title": "Product: Premium Widget",
      "quantity": 2,
      "total": 199.98,
      "price": 99.99,
      "cartItems": [
        {
          "id": "item-uuid-101",
          "productName": "Premium Widget",
          "variantName": "Large - Blue",
          "quantity": 2,
          "price": 99.99,
          "lastModifiedByAdmin": {
            "id": "admin-uuid-1",
            "firstName": "Admin",
            "lastName": "User"
          },
          "lastAdminAction": "add",
          "lastAdminModifiedAt": "2024-01-15T14:20:00Z"
        }
      ]
    }
  ],
  "itemCount": 2,
  "totalValue": 199.98
}
```

---

## 3. Get Specific Cart

**Endpoint:** `GET /v1/admin/carts/:id`

**Purpose:** Retrieve detailed information about a specific cart

**Path Parameters:**

- `id`: Cart's unique identifier

**Example Request:**

```bash
GET /v1/admin/carts/cart-uuid-456
```

**Response:** Same structure as "Get or Create User Cart" response

---

## 4. Delete Cart

**Endpoint:** `DELETE /v1/admin/carts/:id`

**Purpose:** Permanently delete a cart and all its contents (hard delete)

**Path Parameters:**

- `id`: Cart's unique identifier

**Example Request:**

```bash
DELETE /v1/admin/carts/cart-uuid-456
```

**Response:**

```json
{
  "message": "Cart deleted successfully"
}
```

---

## 5. Add Product to Cart

**Endpoint:** `POST /v1/admin/carts/:cartId/items`

**Purpose:** Add a product variant to a user's cart

**Path Parameters:**

- `cartId`: Cart's unique identifier

**Request Body:**

```json
{
  "variantId": "variant-uuid-123",
  "quantity": 3,
  "affiliateId": "affiliate-uuid-456",
  "notes": "Added by admin for customer request"
}
```

**Validation Rules:**

- `variantId`: Required, must be valid product variant ID
- `quantity`: Required, must be positive integer
- `affiliateId`: Optional, must be valid affiliate ID if provided
- `notes`: Optional, admin modification notes

**Example Request:**

```bash
POST /v1/admin/carts/cart-uuid-456/items
Content-Type: application/json

{
  "variantId": "variant-uuid-123",
  "quantity": 2,
  "notes": "Customer requested additional items"
}
```

**Response:**

```json
{
  "id": "section-uuid-789",
  "cartId": "cart-uuid-456",
  "section": "product",
  "title": "Product: Premium Widget",
  "quantity": 2,
  "total": 199.98,
  "price": 99.99,
  "cartItems": [
    {
      "id": "item-uuid-101",
      "productName": "Premium Widget",
      "variantName": "Large - Blue",
      "quantity": 2,
      "price": 99.99,
      "lastModifiedByAdminId": "admin-uuid-1",
      "lastAdminAction": "add",
      "lastAdminModifiedAt": "2024-01-15T14:20:00Z",
      "lastAdminModificationNotes": "Customer requested additional items"
    }
  ]
}
```

---

## 6. Add Bundle to Cart

**Endpoint:** `POST /v1/admin/carts/:cartId/bundles`

**Purpose:** Add a bundle product to a user's cart

**Path Parameters:**

- `cartId`: Cart's unique identifier

**Request Body:**

```json
{
  "bundleId": "bundle-uuid-789",
  "quantity": 1,
  "affiliateId": "affiliate-uuid-456",
  "notes": "Added bundle promotion"
}
```

**Validation Rules:**

- `bundleId`: Required, must be valid bundle ID
- `quantity`: Required, must be positive integer
- `affiliateId`: Optional, must be valid affiliate ID if provided
- `notes`: Optional, admin modification notes

**Example Request:**

```bash
POST /v1/admin/carts/cart-uuid-456/bundles
Content-Type: application/json

{
  "bundleId": "bundle-uuid-789",
  "quantity": 1,
  "notes": "Applied holiday bundle promotion"
}
```

**Response:**

```json
{
  "id": "section-uuid-101",
  "cartId": "cart-uuid-456",
  "section": "bundle",
  "title": "Bundle: Holiday Special",
  "quantity": 1,
  "total": 149.99,
  "price": 149.99,
  "bundleId": "bundle-uuid-789",
  "cartItems": [
    {
      "id": "item-uuid-201",
      "productName": "Widget A",
      "variantName": "Standard",
      "quantity": 1,
      "price": 79.99,
      "bundleName": "Holiday Special",
      "lastModifiedByAdminId": "admin-uuid-1",
      "lastAdminAction": "add",
      "lastAdminModifiedAt": "2024-01-15T14:20:00Z"
    },
    {
      "id": "item-uuid-202",
      "productName": "Widget B",
      "variantName": "Premium",
      "quantity": 1,
      "price": 69.99,
      "bundleName": "Holiday Special",
      "lastModifiedByAdminId": "admin-uuid-1",
      "lastAdminAction": "add",
      "lastAdminModifiedAt": "2024-01-15T14:20:00Z"
    }
  ]
}
```

---

## 7. Update Cart Section Quantity

**Endpoint:** `PUT /v1/admin/carts/sections/:cartSectionId/quantity`

**Purpose:** Update the quantity of a cart section

**Path Parameters:**

- `cartSectionId`: Cart section's unique identifier

**Request Body:**

```json
{
  "quantity": 5,
  "notes": "Updated quantity based on customer request"
}
```

**Validation Rules:**

- `quantity`: Required, must be non-negative integer (0 removes the section)

**Example Request:**

```bash
PUT /v1/admin/carts/sections/section-uuid-789/quantity
Content-Type: application/json

{
  "quantity": 5,
  "notes": "Customer requested quantity increase"
}
```

**Response (Quantity > 0):**

```json
{
  "id": "section-uuid-789",
  "cartId": "cart-uuid-456",
  "section": "product",
  "title": "Product: Premium Widget",
  "quantity": 5,
  "total": 499.95,
  "price": 99.99,
  "lastModifiedByAdminId": "admin-uuid-1",
  "lastAdminAction": "update",
  "lastAdminModifiedAt": "2024-01-15T14:25:00Z"
}
```

**Response (Quantity = 0):**

```json
{
  "message": "Cart section removed (quantity set to 0)"
}
```

---

## 8. Remove Cart Section

**Endpoint:** `DELETE /v1/admin/carts/sections/:cartSectionId`

**Purpose:** Remove an entire cart section and all its items

**Path Parameters:**

- `cartSectionId`: Cart section's unique identifier

**Request Body:**

```json
{
  "notes": "Removed at customer request"
}
```

**Example Request:**

```bash
DELETE /v1/admin/carts/sections/section-uuid-789
Content-Type: application/json

{
  "notes": "Customer no longer wants this item"
}
```

**Response:**

```json
{
  "message": "Item removed successfully"
}
```

---

## 9. Modify Cart Item

**Endpoint:** `PUT /v1/admin/carts/items/:cartItemId`

**Purpose:** Modify individual cart item properties (quantity, price)

**Path Parameters:**

- `cartItemId`: Cart item's unique identifier

**Request Body:**

```json
{
  "quantity": 3,
  "price": 89.99,
  "notes": "Applied special pricing for customer"
}
```

**Validation Rules:**

- `quantity`: Required, must be positive integer
- `price`: Optional, must be positive number if provided
- `notes`: Optional, admin modification notes

**Example Request:**

```bash
PUT /v1/admin/carts/items/item-uuid-101
Content-Type: application/json

{
  "quantity": 3,
  "price": 89.99,
  "notes": "Applied bulk discount pricing"
}
```

**Response:**

```json
{
  "id": "item-uuid-101",
  "cartSectionId": "section-uuid-789",
  "productId": "product-uuid-123",
  "variantId": "variant-uuid-456",
  "quantity": 3,
  "price": 89.99,
  "productName": "Premium Widget",
  "variantName": "Large - Blue",
  "lastModifiedByAdminId": "admin-uuid-1",
  "lastAdminAction": "modify",
  "lastAdminModifiedAt": "2024-01-15T14:30:00Z",
  "lastAdminModificationNotes": "Applied bulk discount pricing"
}
```

---

## 10. Remove Bundle from Cart Section

**Endpoint:** `DELETE /v1/admin/carts/sections/:cartSectionId/bundle`

**Purpose:** Remove bundle association from a cart section while keeping individual items

**Path Parameters:**

- `cartSectionId`: Cart section's unique identifier

**Request Body:**

```json
{
  "notes": "Removed bundle discount at customer request"
}
```

**Example Request:**

```bash
DELETE /v1/admin/carts/sections/section-uuid-101/bundle
Content-Type: application/json

{
  "notes": "Customer prefers individual item pricing"
}
```

**Response:**

```json
{
  "id": "section-uuid-101",
  "cartId": "cart-uuid-456",
  "section": "product",
  "title": "",
  "quantity": 1,
  "total": 149.98,
  "price": 149.98,
  "bundleId": null,
  "cartItems": [
    {
      "id": "item-uuid-201",
      "productName": "Widget A",
      "variantName": "Standard",
      "quantity": 1,
      "price": 79.99,
      "bundleName": null,
      "bundleDiscountId": null
    },
    {
      "id": "item-uuid-202",
      "productName": "Widget B",
      "variantName": "Premium",
      "quantity": 1,
      "price": 69.99,
      "bundleName": null,
      "bundleDiscountId": null
    }
  ]
}
```

---

## API Usage Flow and Order

### **Typical Cart Management Workflow**

1. **Get User Cart** → `GET /v1/admin/carts/user/:userId`

   - Use this to see current cart state
   - Creates new cart if none exists

2. **Add Products** → `POST /v1/admin/carts/:cartId/items`

   - Add individual products to cart
   - Can add multiple times for same variant

3. **Add Bundles** → `POST /v1/admin/carts/:cartId/bundles`

   - Add promotional bundles
   - Automatically creates cart items for bundle contents

4. **Modify Quantities** → `PUT /v1/admin/carts/sections/:cartSectionId/quantity`

   - Adjust quantities as needed
   - Set to 0 to remove section

5. **Modify Individual Items** → `PUT /v1/admin/carts/items/:cartItemId`

   - Fine-tune individual item properties
   - Apply special pricing if needed

6. **Remove Items** → `DELETE /v1/admin/carts/sections/:cartSectionId`

   - Remove entire sections when needed

7. **Manage Bundles** → `DELETE /v1/admin/carts/sections/:cartSectionId/bundle`

   - Remove bundle discounts while keeping items

8. **Final Review** → `GET /v1/admin/carts/:cartId`
   - Review final cart state before checkout

### **When to Use Each API**

| **Use Case**        | **Primary API**                              | **Alternative**                                    | **Notes**                        |
| ------------------- | -------------------------------------------- | -------------------------------------------------- | -------------------------------- |
| **View User Cart**  | `GET /v1/admin/carts/user/:userId`           | `GET /v1/admin/carts/:id`                          | Creates cart if none exists      |
| **Add New Product** | `POST /v1/admin/carts/:cartId/items`         | -                                                  | Handles duplicates automatically |
| **Add Bundle**      | `POST /v1/admin/carts/:cartId/bundles`       | -                                                  | Creates multiple cart items      |
| **Change Quantity** | `PUT /v1/admin/carts/sections/:id/quantity`  | -                                                  | Updates entire section           |
| **Modify Price**    | `PUT /v1/admin/carts/items/:id`              | -                                                  | Individual item modification     |
| **Remove Section**  | `DELETE /v1/admin/carts/sections/:id`        | `PUT /v1/admin/carts/sections/:id/quantity` with 0 | Complete removal                 |
| **Remove Bundle**   | `DELETE /v1/admin/carts/sections/:id/bundle` | -                                                  | Keeps individual items           |
| **Delete Cart**     | `DELETE /v1/admin/carts/:id`                 | -                                                  | Permanent deletion               |

---

## Error Handling

### **Common Error Responses**

**400 Bad Request:**

```json
{
  "success": false,
  "message": "Failed to add item to cart",
  "error": "Product variant not found"
}
```

**401 Unauthorized:**

```json
{
  "success": false,
  "message": "Authentication required"
}
```

**403 Forbidden:**

```json
{
  "success": false,
  "message": "Insufficient permissions"
}
```

**404 Not Found:**

```json
{
  "success": false,
  "message": "Cart not found"
}
```

### **Error Scenarios and Solutions**

| **Error**                        | **Cause**                    | **Solution**                                  |
| -------------------------------- | ---------------------------- | --------------------------------------------- |
| `Cart not found`                 | Invalid cart ID              | Verify cart exists with `GET /v1/admin/carts` |
| `Product variant not found`      | Invalid variant ID           | Check product catalog for valid variant IDs   |
| `Variant not available for sale` | Product out of stock         | Check inventory status                        |
| `Bundle product not found`       | Invalid bundle ID            | Verify bundle exists and is active            |
| `Insufficient permissions`       | Admin lacks CART permissions | Ensure admin has proper role assignments      |

---

## Performance Considerations

### **Optimization Features**

- **Concurrent Operations**: All database operations use `Promise.all()` for maximum performance
- **Batch Updates**: Bulk operations available for multiple item modifications
- **Smart Preloading**: Efficient relationship loading to minimize database queries
- **Bundle Intelligence**: Optimized bundle matching with Jaccard similarity scoring

### **Best Practices**

1. **Use Bulk Operations**: For multiple modifications, use bulk endpoints when available
2. **Limit Preloading**: Only preload relationships you actually need
3. **Batch Requests**: Group related operations together
4. **Cache Results**: Cache cart data for frequently accessed carts
5. **Monitor Performance**: Track response times for large carts

---

## Audit Trail

### **Admin Action Tracking**

Every cart modification is automatically tracked with:

- **Admin ID**: Who made the change
- **Action Type**: What was done (add, update, remove, modify)
- **Timestamp**: When the change occurred
- **Notes**: Optional explanation for the change

### **Audit Fields in Responses**

```json
{
  "lastModifiedByAdminId": "admin-uuid-1",
  "lastAdminAction": "add",
  "lastAdminModifiedAt": "2024-01-15T14:20:00Z",
  "lastAdminModificationNotes": "Customer requested additional items"
}
```

### **Audit History Retrieval**

Use `GET /v1/admin/carts/:id` to retrieve full audit history for any cart.

---

## Testing and Validation

### **Test Scenarios**

1. **Empty Cart Operations**: Test adding items to empty carts
2. **Duplicate Items**: Verify quantity updates for existing items
3. **Bundle Management**: Test bundle creation and removal
4. **Permission Testing**: Verify role-based access control
5. **Error Handling**: Test with invalid IDs and edge cases

### **Validation Checklist**

- [ ] All required fields are validated
- [ ] Quantity values are positive integers
- [ ] Price values are positive numbers
- [ ] IDs reference valid entities
- [ ] Admin permissions are enforced
- [ ] Audit trail is properly recorded

---

## Support and Troubleshooting

### **Common Issues**

1. **Permission Denied**: Ensure admin has CART resource permissions
2. **Validation Errors**: Check request body format and required fields
3. **Performance Issues**: Use bulk operations for large carts
4. **Audit Trail Missing**: Verify admin ID is properly passed

### **Getting Help**

- Check API response messages for specific error details
- Verify admin permissions and role assignments
- Review audit trail for operation history
- Monitor performance metrics for optimization opportunities

---

## Version History

- **v1.0**: Initial release with full cart management capabilities
- **Features**: Add, update, remove items and bundles with audit trail
- **Performance**: Concurrent operations and batch processing
- **Security**: Role-based access control and input validation
