import ZnCartSection from '#models/zn_cart_section'
import ZnCartItem from '#models/zn_cart_item'
import { ShopCartService } from '#services/shop/shop_cart_service'
import logger from '@adonisjs/core/services/logger'

export interface IAdminAddItemToCart {
  cartId: string
  variantId: string
  quantity: number
  affiliateId?: string | null
  notes?: string
}

export interface IAdminAddBundleToCart {
  cartId: string
  bundleId: string
  quantity: number
  discountId?: string | null
  affiliateId?: string | null
  notes?: string
  items?: Array<{
    variantId: string
    itemId: string
  }>
  collections?: Array<{
    id: string
    items: Array<{
      id: string
      variants: Array<{
        id: string
        quantity: number
      }>
    }>
  }>
}

export interface IAdminUpdateCartItem {
  cartItemId: string
  quantity?: number
  price?: number
  notes?: string
}

export interface IAdminUpdateSectionQuantity {
  cartSectionId: string
  quantity: number
  notes?: string
}

export class AdminCartService {
  private shopCartService: ShopCartService

  constructor() {
    this.shopCartService = new ShopCartService()
  }

  /**
   * Add item to cart - sử dụng logic giống app API 100%
   */
  async addItemToCart(payload: IAdminAddItemToCart): Promise<ZnCartSection> {
    try {
      // Sử dụng ShopCartService để đảm bảo logic giống 100%
      const result = await this.shopCartService.addItemToCart({
        cartId: payload.cartId,
        variantId: payload.variantId,
        quantity: payload.quantity,
        affiliateId: payload.affiliateId || undefined,
        userId: undefined, // Admin không cần userId
      })

      // Log admin action nếu có notes
      if (payload.notes) {
        logger.info('Admin added item to cart', {
          cartId: payload.cartId,
          variantId: payload.variantId,
          quantity: payload.quantity,
          notes: payload.notes,
        })
      }

      if (!result) {
        throw new Error('Failed to add item to cart')
      }
      return result
    } catch (error) {
      logger.error('Failed to add item to cart', { error: error.message, payload })
      throw error
    }
  }

  /**
   * Add bundle to cart - sử dụng logic giống app API 100%
   */
  async addBundleToCart(payload: IAdminAddBundleToCart): Promise<ZnCartSection> {
    try {
      // Sử dụng ShopCartService để đảm bảo logic giống 100%
      const result = await this.shopCartService.addBundleToCart({
        cartId: payload.cartId,
        bundleId: payload.bundleId,
        quantity: payload.quantity,
        discountId: payload.discountId || undefined,
        items: payload.items,
        collections: payload.collections,
        userId: undefined, // Admin không cần userId
      })

      // Log admin action nếu có notes
      if (payload.notes) {
        logger.info('Admin added bundle to cart', {
          cartId: payload.cartId,
          bundleId: payload.bundleId,
          quantity: payload.quantity,
          notes: payload.notes,
        })
      }

      if (!result) {
        throw new Error('Failed to add bundle to cart')
      }
      return result
    } catch (error) {
      logger.error('Failed to add bundle to cart', { error: error.message, payload })
      throw error
    }
  }

  /**
   * Update cart section quantity - sử dụng logic giống app API 100%
   */
  async updateSectionQuantity(payload: IAdminUpdateSectionQuantity): Promise<ZnCartSection | null> {
    try {
      const cartSection = await ZnCartSection.query()
        .where('id', payload.cartSectionId)
        .preload('cartItems', (query) => {
          query.preload('discount').preload('variant').preload('product')
        })
        .preload('bundle', (query) => {
          query.preload('items', (query) => {
            query.preload('product').preload('variants')
          })
          query.preload('discounts')
        })
        .first()

      if (!cartSection) {
        throw new Error('Cart section not found')
      }

      // Sử dụng ShopCartService để đảm bảo logic giống 100%
      const result = await this.shopCartService.updateQuantityCartSection({
        cartSection,
        quantity: payload.quantity,
      })

      // Log admin action nếu có notes
      if (payload.notes) {
        logger.info('Admin updated cart section quantity', {
          cartSectionId: payload.cartSectionId,
          quantity: payload.quantity,
          notes: payload.notes,
        })
      }

      return result
    } catch (error) {
      logger.error('Failed to update section quantity', { error: error.message, payload })
      throw error
    }
  }

  /**
   * Update individual cart item
   */
  async updateCartItem(payload: IAdminUpdateCartItem): Promise<ZnCartItem> {
    try {
      const cartItem = await ZnCartItem.query()
        .where('id', payload.cartItemId)
        .preload('cartSection')
        .preload('product')
        .preload('variant')
        .preload('discount')
        .firstOrFail()

      const updateData: any = {}

      if (payload.quantity !== undefined) {
        updateData.quantity = payload.quantity
      }

      if (payload.price !== undefined) {
        updateData.price = payload.price
      }

      await cartItem.merge(updateData).save()

      // Nếu thay đổi quantity, cần update lại section totals
      if (payload.quantity !== undefined) {
        const cartSection = cartItem.cartSection
        await this.recalculateSectionTotals(cartSection)
      }

      // Log admin action nếu có notes
      if (payload.notes) {
        logger.info('Admin updated cart item', {
          cartItemId: payload.cartItemId,
          updates: updateData,
          notes: payload.notes,
        })
      }

      await cartItem.refresh()
      return cartItem
    } catch (error) {
      logger.error('Failed to update cart item', { error: error.message, payload })
      throw error
    }
  }

  /**
   * Delete cart section - sử dụng logic giống app API 100%
   */
  async deleteCartSection(cartSectionId: string, notes?: string): Promise<void> {
    try {
      const cartSection = await ZnCartSection.query()
        .where('id', cartSectionId)
        .preload('cartItems')
        .firstOrFail()

      // Sử dụng ShopCartService để đảm bảo logic giống 100%
      await this.shopCartService.deleteCartSection(cartSection)

      // Log admin action nếu có notes
      if (notes) {
        logger.info('Admin deleted cart section', {
          cartSectionId,
          notes,
        })
      }
    } catch (error) {
      logger.error('Failed to delete cart section', { error: error.message, cartSectionId })
      throw error
    }
  }

  /**
   * Recalculate section totals after item updates
   */
  private async recalculateSectionTotals(cartSection: ZnCartSection): Promise<void> {
    await cartSection.load('cartItems')

    let total = 0
    let rawTotal = 0
    let quantity = 0

    for (const item of cartSection.cartItems) {
      total += item.price * item.quantity
      if (item.rawPrice) {
        rawTotal += item.rawPrice * item.quantity
      }
      quantity += item.quantity
    }

    await cartSection
      .merge({
        total,
        rawTotal: rawTotal > 0 ? rawTotal : null,
        quantity,
      })
      .save()
  }
}
