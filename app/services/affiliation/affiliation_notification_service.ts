import { ECommissionGroupType } from "#constants/commission_group_type";
import { NOTIFICATION_TYPE } from "#constants/notification";
import TierChangedNotification from "#mails/affiliation/tier_changed_notification";
import AppMail from "#mails/app_mail";
import Notification from "#models/notification";
import ZnAffiliate from "#models/zn_affiliate";
import ZnUser from "#models/zn_user";
import { NotificationService } from "#services/notification_service";
import logger from "@adonisjs/core/services/logger";
import mail from "@adonisjs/mail/services/main";
import env from '#start/env';
import { AdminNotificationService } from "../../../admin/services/notification/admin_notification_service.js";
import { ACTION, RESOURCE } from "#constants/authorization";
import CommissionRefundNotification from "#mails/affiliation/commission_refund_notification";
import ZnAffiliateCommission from "#models/zn_affiliate_commission";
import ZnOrder from "#models/zn_order";
import formatUSD from "../../../admin/utils/currency-formatter.js";
import CommissionCancelNotification from "#mails/affiliation/commission_cancel_notification";

export default class AffiliationNotificationService {
  private adminNotificationService: AdminNotificationService;

  constructor() {
    this.adminNotificationService = new AdminNotificationService();
  }

  async sendTierChangedNotification(affiliate: ZnAffiliate, isUpgrading: boolean) {
    await affiliate.load('affiliateTier', (query) => {
      query
        .preload('commissionGroups', (query: any) => {
          query
            .where('type', ECommissionGroupType.DEFAULT)
            .preload('commissionRates');
        })
    });

    if (!affiliate.affiliateTier ||
      affiliate.affiliateTier.commissionGroups.length === 0 ||
      affiliate.affiliateTier.commissionGroups[0].commissionRates.length === 0) {
      logger.error(`Cannot send tier changed email to ${affiliate.user.email}. The tier is not fully configured.`);
      return;
    }

    if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.AFFILIATION },
      ])
      for (const admin of admins) {
        await this.sendEmail(
          new TierChangedNotification(
            affiliate.user.firstName,
            isUpgrading,
            affiliate.affiliateTier.commissionGroups[0].commissionRates,
            affiliate.user.email
          ),
          admin.username
        );
      }
    } else {
      await this.sendEmail(
        new TierChangedNotification(
          affiliate.user.firstName,
          isUpgrading,
          affiliate.affiliateTier.commissionGroups[0].commissionRates,
          affiliate.user.email
        ),
        affiliate.user.email
      );

      await this.sendPushNotification(
        affiliate.user,
        affiliate.id,
        isUpgrading ? 'Tier Upgraded!' : 'Tier Changed',
        isUpgrading
          ? 'Congratulations! Your affiliate tier has been upgraded. Check your new commission rates.'
          : 'Your affiliate tier has been downgraded. Review your updated commission rates in your dashboard.'
      )
    }
  }

  async sendCommissionRefundedNotification(order: ZnOrder, commission: ZnAffiliateCommission) {
    await order.load('user');
    await commission.load('affiliate', (query) => {
      query.preload('user');
    });

    const orderTimeString = order.createdAt.toFormat('EEE, MMM dd, yyyy, hh:mm a');
    const audienceName = `${order.user.firstName} ${order.user.lastName}`;
    const totalPrice = formatUSD(order.totalPrice);
    const commissionAmount = formatUSD(commission.finalAmount);

    if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
      ]);

      for (const admin of admins) {
        await this.sendEmail(new CommissionRefundNotification(
          commission.affiliate.user.firstName,
          commission.id,
          orderTimeString,
          audienceName,
          totalPrice,
          commissionAmount,
          admin.username
        ),
          admin.username
        );
      }
    } else {
      await this.sendEmail(new CommissionRefundNotification(
        commission.affiliate.user.firstName,
        commission.id,
        orderTimeString,
        audienceName,
        totalPrice,
        commissionAmount,
        commission.affiliate.user.email
      ),
        commission.affiliate.user.email
      );

      await this.sendPushNotification(
        commission.affiliate.user,
        'commissions',
        'Commission Revoked',
        'A commission was revoked due to a customer refund. Check your dashboard for details.'
      );
    }
  }

  async sendCommissionCancelledNotification(order: ZnOrder, commission: ZnAffiliateCommission, previousAmount: number) {
    await order.load('user');
    await commission.load('affiliate', (query) => {
      query.preload('user');
    });

    const orderCancelledTimeString = order.updatedAt.toFormat('EEE, MMM dd, yyyy, hh:mm a');
    const audienceName = `${order.user.firstName} ${order.user.lastName}`;
    const orderTotalString = formatUSD(order.totalPrice);
    const commissionAmountString = formatUSD(previousAmount);

    if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
      // const admins = await ZnAdmin.all()
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
      ])
      admins.forEach(async (admin) => {
        await this.sendEmail(new CommissionCancelNotification(
          commission.affiliate.user.firstName,
          commission.id,
          orderCancelledTimeString,
          audienceName,
          orderTotalString,
          commissionAmountString,
          admin.username
        ),
          admin.username
        );
      });
    } else {
      await this.sendEmail(new CommissionCancelNotification(
        commission.affiliate.user.firstName,
        commission.id,
        orderCancelledTimeString,
        audienceName,
        orderTotalString,
        commissionAmountString,
        commission.affiliate.user.email
      ),
        commission.affiliate.user.email
      );

      await this.sendPushNotification(
        commission.affiliate.user,
        'commissions',
        'Commission Cancelled',
        'A commission was cancelled because the order was cancelled. Check your dashboard for details.'
      );
    }
  }

  private async sendEmail(emailHandler: AppMail, userEmail: string) {
    await mail
      .send(emailHandler)
      .then(() => {
        logger.info(`${Object.getPrototypeOf(emailHandler).constructor.name} has been sent successfully to ${userEmail}`);
      })
      .catch((error) => {
        console.error('Error when sending email', error);
      })
  }

  private async sendPushNotification(
    user: ZnUser,
    resourceId: string,
    title: string,
    description: string
  ) {
    const notification = await Notification.create({
      type: NOTIFICATION_TYPE.AFFILIATE,
      userId: user.id,
      resourceId: resourceId,
      title: title,
      description: description,
    })

    if (notification) {
      const notificationService = new NotificationService()
      await notificationService.send([user], [notification])
    }
  }
}