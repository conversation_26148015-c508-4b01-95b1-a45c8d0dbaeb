import { EApprovalStatus } from '#constants/approval_status'
import { ACTION, RESOURCE } from '#constants/authorization'
import { MEDIA_TYPE } from '#constants/media'
import { NOTIFICATION_TYPE } from '#constants/notification'
import AdminReceiveRegistrationNotification from '#mails/affiliation/admin_registration_notification'
import PayoutNotification from '#mails/affiliation/payout_notification'
import RefCodeAssignedNotification from '#mails/affiliation/refcode_assigned_notification'
import RegistrationApprovedNotification from '#mails/affiliation/registration_approved_notification'
import RegistrationConfirmationNotification from '#mails/affiliation/registration_confirmation_notification'
import RegistrationReapplyNotification from '#mails/affiliation/registration_reapply_notification'
import RegistrationRejectNotification from '#mails/affiliation/registration_reject_notification'
import Notification from '#models/notification'
import ZnAffiliate from '#models/zn_affiliate'
import ZnAffiliateCommission from '#models/zn_affiliate_commission'
import ZnAffiliateCommissionPayment from '#models/zn_affiliate_commission_payment'
import ZnAffiliateSocialPage from '#models/zn_affiliate_social_page'
import ZnAffiliateTier from '#models/zn_affiliate_tier'
import ZnOrder from '#models/zn_order'
import ZnPaymentMethod from '#models/zn_payment_method'
import ZnUser from '#models/zn_user'
import ZnPost, { EPostType } from '#models/zn_post'
import ZnTracking from '#models/zn_tracking'
import { NotificationService } from '#services/notification_service'
import env from '#start/env'
import { approvalRegistrationValidator } from '#validators/affiliate'
import logger from '@adonisjs/core/services/logger'
import mail from '@adonisjs/mail/services/main'
import { addDays, endOfMonth, format, startOfMonth } from 'date-fns'
import { DateTime } from 'luxon'
import db from '@adonisjs/lucid/services/db'
import { TRACKING_ACTION } from '#constants/tracking'
import { AdminNotificationService } from '../../../admin/services/notification/admin_notification_service.js'
import { AffiliationCommissionService } from './affiliation_commission_service.js'
import { AffiliateRefCodeService } from './affiliation_refcode_service.js'
import { AffiliateTierService } from './affiliation_tier_service.js'
import ZnResourceInteracts from '#models/zn_resource_interacts'
import { formatDBDate } from '../../../services/commons.js'
import { EPaymentType } from '#constants/payment_type'
import { AffiliationSnapshotsService } from './affiliation_snapshots_service.js'
import { EAffiliateSnapshotKey } from '#constants/affiliate_snapshot_enum'
import { ECommissionGroupType } from '#constants/commission_group_type'
import ZnDraftOrder from '#models/zn_draft_order'
import AffiliationNotificationService from './affiliation_notification_service.js'

export interface IAffiliateRegistrationData {
  userId: string
  socialPages: string[]
}

export interface IDiscountCodeBasicInput {
  title: string
  code: string
  startsAt: DateTime
  endsAt?: DateTime
  customerGets: IDiscountCustomerGetsInput
  customerSelection: IDiscountCustomerSelectionInput
}

export interface IDiscountCustomerGetsInput {
  value: IDiscountCodeCustomerGetValue
  items: IDiscountCodeItemsInput
}

export interface IDiscountCodeCustomerGetValue {
  percentage: number
}

export interface IDiscountCodeItemsInput {
  all: boolean
}

export interface IDiscountCustomerSelectionInput {
  all: boolean
}

export interface IDateRange {
  from: Date | null
  to: Date | null
}

export interface IAffiliateStatistic {
  totalSoldItems: number
  totalRevenue: number
  refCodeUsed: number
  estimatedCommissionsAmount: number
  totalSales: number
  commissionRate: number
  commissionRangeFrom: number
  commissionRangeTo: number
}

export interface IVideoReportData {
  videoCount: number
  totalViews: number
  totalProducts: number
  itemsSold: number
}

enum ETierAction {
  UPGRADE = 'upgrade',
  DOWNGRADE = 'downgrade',
}

export class AffiliationService {
  private tierService: AffiliateTierService
  private refCodeService: AffiliateRefCodeService
  private commissionService: AffiliationCommissionService
  private adminNotificationService: AdminNotificationService
  private affiliationNotificationService: AffiliationNotificationService
  private affiliationSnapshotsService: AffiliationSnapshotsService

  constructor() {
    this.tierService = new AffiliateTierService()
    this.refCodeService = new AffiliateRefCodeService()
    this.commissionService = new AffiliationCommissionService()
    this.adminNotificationService = new AdminNotificationService()
    this.affiliationNotificationService = new AffiliationNotificationService()
    this.affiliationSnapshotsService = new AffiliationSnapshotsService()
  }

  async getAffiliate(affiliateId: string) {
    return await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('user')
      .preload('affiliateTier')
      .preload('commissionPayments', (query) => {
        query
          .preload('paymentMethod', (query) => {
            query.withScopes((scopes) => scopes.withAll())
          })
          .orderBy('createdAt', 'desc')
      })
      .preload('socialPages')
      .preload('refCode')
      .preload('paymentMethods', (query) => {
        query
          .preload('paypalDetail')
          .preload('directDepositDetail')
          .preload('achTransferDetail')
          .preload('otherPaymentDetail')
      })
      .first()
  }

  async getReferredCustomers(affiliateId: string) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('referredCustomers')
      .firstOrFail();

    return affiliate.referredCustomers;
  }

  async getReferredCustomersCount(affiliateId: string) {
    return await this.affiliationSnapshotsService.getSnapshotAsNumber(affiliateId, EAffiliateSnapshotKey.REFERRED_CUSTOMERS_COUNT);
  }

  async getStatistics(affiliateId: string, range: IDateRange): Promise<IAffiliateStatistic> {
    const query = ZnAffiliateCommission.query()
      .preload('commissionDetails', (query) => {
        query.preload('orderDetail')
      })
      .where('affiliateId', affiliateId)

    if (range.from) {
      query.where('createdAt', '>=', range.from)
    }

    if (range.to) {
      query.where('createdAt', '<', addDays(range.to, 1))
    }

    if (!range.from && !range.to) {
      query.where('createdAt', '>=', startOfMonth(new Date()))
      query.where('createdAt', '<', addDays(endOfMonth(new Date()), 1))
    }

    const commissions = await query

    let totalSoldItems = 0
    let totalRevenue = 0
    let estimatedCommissionsAmount = 0
    for (const commission of commissions) {
      if (commission.status === EApprovalStatus.APPROVED) {
        for (const commissionDetail of commission.commissionDetails) {
          if (
            commissionDetail.orderDetail.currentQuantity > 0 &&
            commissionDetail.commissionRate > 0
          ) {
            totalSoldItems += commissionDetail.orderDetail.currentQuantity
            totalRevenue +=
              commissionDetail.orderDetail.price * commissionDetail.orderDetail.currentQuantity -
              commissionDetail.orderDetail.discount
          }
        }
      }
      if (commission.status !== EApprovalStatus.REJECTED) {
        estimatedCommissionsAmount += commission.finalAmount
      }
    }
    const refCodeUsed = commissions.length

    const snapshots = await this.affiliationSnapshotsService.getSnapshots(affiliateId, ECommissionGroupType.DEFAULT);

    return {
      totalSoldItems: parseFloat(totalSoldItems.toFixed(2)),
      totalRevenue: parseFloat(totalRevenue.toFixed(2)),
      refCodeUsed: parseFloat(refCodeUsed.toFixed(2)),
      estimatedCommissionsAmount: parseFloat(estimatedCommissionsAmount.toFixed(2)),
      ...snapshots,
    }
  }

  async updateSnapshots(affiliateId: string, date: Date) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('snapshots')
      .firstOrFail();

    const startDate = this.getStartDateOfMonth(date);
    const endDate = this.getEndDateOfMonth(date);

    const totalSales = await this.affiliationSnapshotsService.updateTotalSalesSnapshot(affiliate, startDate, endDate);
    await this.affiliationSnapshotsService.updateCommissionRateSnapshot(affiliate, ECommissionGroupType.DEFAULT, totalSales, startDate);
    await this.affiliationSnapshotsService.updateCommissionRateSnapshot(affiliate, ECommissionGroupType.FLASH_SALE, totalSales, startDate);
    await this.affiliationSnapshotsService.updateReferredCustomersCountSnapshot(affiliate);
    await this.affiliationSnapshotsService.updatePendingCommissionsSnapshot(affiliate);

    await affiliate.load('snapshots', (query) => {
      query.orderBy('key', 'asc');
    });
    return affiliate.snapshots;
  }

  async getCommissions(affiliateId: string, range: IDateRange, page: number, limit: number) {
    const query = ZnAffiliateCommission.query().where('affiliateId', affiliateId)

    if (range.from) {
      query.where('createdAt', '>=', range.from)
    }

    if (range.to) {
      query.where('createdAt', '<', addDays(range.to, 1))
    }

    if (!range.from && !range.to) {
      query.where('createdAt', '>=', startOfMonth(new Date()))
      query.where('createdAt', '<', addDays(endOfMonth(new Date()), 1))
    }

    query.preload('order').orderBy('createdAt', 'desc')

    return await query.paginate(page, limit)
  }

  async getPayouts(affiliateId: string, range: IDateRange) {
    const query = ZnAffiliateCommissionPayment.query().where('affiliateId', affiliateId)

    if (range.from) {
      query.where('createdAt', '>=', range.from)
    }

    if (range.to) {
      query.where('createdAt', '<', addDays(range.to, 1))
    }

    query.preload('paymentMethod', (query) => {
      query.withScopes((scopes) => scopes.withAll())
    })
    query.orderBy('createdAt', 'desc')

    return await query
  }

  async getPaymentMethods(affiliateId: string) {
    const paymentMethods = await ZnPaymentMethod.query()
      .whereHas('affiliates', (query) => {
        query.where('id', affiliateId)
      })
      .preload('paypalDetail')
      .preload('directDepositDetail')
      .preload('achTransferDetail')
      .preload('otherPaymentDetail')

    return [
      ...paymentMethods.filter((pm) => pm.paymentType === EPaymentType.PAYPAL),
      ...paymentMethods.filter((pm) => pm.paymentType === EPaymentType.DIRECT_DEPOSIT),
      ...paymentMethods.filter((pm) => pm.paymentType === EPaymentType.ACH_TRANSFER),
      ...paymentMethods.filter((pm) => pm.paymentType === EPaymentType.OTHER),
    ]
  }

  async registerAffiliate(registrationData: IAffiliateRegistrationData) {
    const registeredAffiliate = await ZnAffiliate.findBy('userId', registrationData.userId)
    if (registeredAffiliate) {
      return {
        success: false,
        message: 'This user is already registered',
        data: registeredAffiliate.serialize(),
      }
    }

    const createdAffiliate = await ZnAffiliate.create(registrationData)

    for (const pageLink of registrationData.socialPages) {
      await ZnAffiliateSocialPage.create({ link: pageLink, affiliateId: createdAffiliate.id })
    }

    const updatedAffiliate = await ZnAffiliate.query()
      .where('id', createdAffiliate.id)
      .preload('socialPages')
      .preload('user')
      .firstOrFail()

    if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
      // const admins = await ZnAdmin.all()
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.AFFILIATION },
      ])
      admins.forEach(async (admin) => {
        await mail
          .send(
            new RegistrationConfirmationNotification(
              updatedAffiliate.user.firstName,
              admin.username
            )
          )
          .then(() => {
            logger.info(
              `Registration confirmation email has been sent successfully to ${admin.username}`
            )
          })
          .catch((error) => {
            console.error('Error when sending email:', error)
          })
      })
    } else {
      await mail
        .send(
          new RegistrationConfirmationNotification(
            updatedAffiliate.user.firstName,
            updatedAffiliate.user.email
          )
        )
        .then(() => {
          logger.info(
            `Registration confirmation email has been sent successfully to ${updatedAffiliate.user.email}`
          )
        })
        .catch((error) => {
          console.error('Error when sending email:', error)
        })

      await this.sendPushNotification(
        updatedAffiliate.user,
        updatedAffiliate.id,
        'Application Received',
        "Thanks for applying! Our team is reviewing your info. We'll notify you soon."
      )
    }

    // const admins = await ZnAdmin.all()
    const admins = await this.adminNotificationService.getAdminsByPermissions([
      { action: ACTION.READ, resource: RESOURCE.AFFILIATION },
    ])
    admins.forEach(async (admin) => {
      await mail
        .send(
          new AdminReceiveRegistrationNotification(
            true,
            admin.name ?? 'Admin',
            admin.username,
            `${updatedAffiliate.user.firstName} ${updatedAffiliate.user.lastName}`,
            updatedAffiliate.user.email,
            updatedAffiliate.socialPages.map((page) => page.link).join(', '),
            updatedAffiliate.createdAt.toFormat('EEE, MMM dd, yyyy, hh:mm a')
          )
        )
        .then(() => {
          logger.info(
            `New registration notification email has been sent successfully to ${admin.username}`
          )
        })
        .catch((error) => {
          console.error('Error when sending email:', error)
        })
    })

    return {
      success: true,
      message: 'Registered successfully',
      data: updatedAffiliate?.serialize(),
    }
  }

  async registerAffiliateForExistingUser(userId: string) {
    const user = await ZnUser.findOrFail(userId)

    const existingAffiliate = await ZnAffiliate.findBy('userId', user.id)
    if (existingAffiliate !== null && existingAffiliate !== undefined) return existingAffiliate

    const initialTier = await this.tierService.getStartingTier()

    return await ZnAffiliate.create({
      userId: user.id,
      tierId: initialTier.id,
      registerStatus: EApprovalStatus.APPROVED,
      shareLinkWithRefCode: true,
    })
  }

  async updateAffiliateRegistration(registrationData: IAffiliateRegistrationData) {
    const pendingAffiliate = await ZnAffiliate.query()
      .preload('socialPages')
      .where('userId', registrationData.userId)
      .firstOrFail()

    if (pendingAffiliate.registerStatus === EApprovalStatus.APPROVED) {
      throw new Error('The registration was already approved. Updating is not allowed.')
    }

    pendingAffiliate.registerStatus = EApprovalStatus.PENDING
    await pendingAffiliate.save()

    pendingAffiliate.socialPages.forEach(async (page) => {
      await page.delete()
    })

    for (const pageLink of registrationData.socialPages) {
      await ZnAffiliateSocialPage.create({ link: pageLink, affiliateId: pendingAffiliate.id })
    }

    const updatedAffiliate = await ZnAffiliate.query()
      .where('id', pendingAffiliate.id)
      .preload('socialPages')
      .preload('user')
      .firstOrFail()

    if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
      // const admins = await ZnAdmin.all()
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.AFFILIATION },
      ])
      admins.forEach(async (admin) => {
        await mail
          .send(
            new RegistrationReapplyNotification(updatedAffiliate.user.firstName, admin.username)
          )
          .then(() => {
            logger.info(
              `Confirmation email of reapplied registration has been sent successfully to ${admin.username}`
            )
          })
          .catch((error) => {
            console.error('Error when sending email:', error)
          })
      })
    } else {
      await mail
        .send(
          new RegistrationReapplyNotification(
            updatedAffiliate.user.firstName,
            updatedAffiliate.user.email
          )
        )
        .then(() => {
          logger.info(
            `Confirmation email of reapplied registration has been sent successfully to ${updatedAffiliate.user.email}`
          )
        })
        .catch((error) => {
          console.error('Error when sending email:', error)
        })

      await this.sendPushNotification(
        updatedAffiliate.user,
        updatedAffiliate.id,
        'Reapplication Received',
        "Your reapplication is under review. We'll update you once it's processed."
      )
    }

    // const admins = await ZnAdmin.all()
    const admins = await this.adminNotificationService.getAdminsByPermissions([
      { action: ACTION.READ, resource: RESOURCE.AFFILIATION },
    ])
    admins.forEach(async (admin) => {
      await mail
        .send(
          new AdminReceiveRegistrationNotification(
            false,
            admin.name ?? 'Admin',
            admin.username,
            `${updatedAffiliate.user.firstName} ${updatedAffiliate.user.lastName}`,
            updatedAffiliate.user.email,
            updatedAffiliate.socialPages.map((page) => page.link).join(', '),
            updatedAffiliate.updatedAt.toFormat('EEE, MMM dd, yyyy, hh:mm a')
          )
        )
        .then(() => {
          logger.info(`Reapply notification email has been sent successfully to ${admin.username}`)
        })
        .catch((error) => {
          console.error('Error when sending email:', error)
        })
    })

    return updatedAffiliate
  }

  async updateShareLinkWithRefCode(userId: string, enable: boolean) {
    const affiliate = await ZnAffiliate.findByOrFail('userId', userId)
    affiliate.shareLinkWithRefCode = enable
    return await affiliate.save()
  }

  generateRandomString(length: number): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    const charactersLength = characters.length
    let result = ''

    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength))
    }

    return result
  }

  async checkReferredItemsForCommissions(order: ZnOrder, shopifyOrder: any) {
    const draftOrderId = shopifyOrder.note_attributes?.find((item: any) => item.name === 'znDraftOrderId')?.value;
    if (!draftOrderId) {
      logger.warn(`The order ${shopifyOrder.admin_graphql_api_id} doesn't contain znDraftOrderId attribute. Cannot check for affiliate commissions.`);
      return;
    }

    const draftOrder = await ZnDraftOrder.query()
      .where('id', draftOrderId)
      .preload('details')
      .first();
    if (!draftOrder) {
      logger.warn(`Cannot find any draft order with ID ${draftOrderId}`);
      return;
    }

    const affiliatesCommissionsMap = new Map<string, any[]>();
    const affiliates: ZnAffiliate[] = [];

    await order.load('orderDetails', (query) => {
      query.preload('variant');
    });
    for (const orderDetail of order.orderDetails) {
      const variantId = orderDetail.variantId;
      const draftOrderDetail = draftOrder.details.find(item => item.variantId === variantId);
      if (!draftOrderDetail) {
        logger.warn(`Cannot find any draft order detail with Variant ID ${variantId}`);
        continue;
      }
      const referredAffiliateId = draftOrderDetail.referredAffiliateId;
      if (!referredAffiliateId) {
        logger.debug(`No affiliate referred item with Variant ID ${variantId}`);
        continue;
      }
      logger.info(`Product variant with ID ${draftOrderDetail.variantId} is referred by affiliate with ID ${referredAffiliateId}`);

      if (!affiliatesCommissionsMap.has(referredAffiliateId)) {
        affiliatesCommissionsMap.set(referredAffiliateId, []);
      }

      let affiliate: ZnAffiliate | undefined | null = affiliates.find(a => a.id === referredAffiliateId);
      if (!affiliate) {
        affiliate = await ZnAffiliate.query()
          .where('id', referredAffiliateId)
          .preload('affiliateTier', (query) => {
            query.preload('commissionGroups', (query) => {
              query.preload('products');
            });
          })
          .first();
        if (affiliate) {
          affiliates.push(affiliate);
        }
      }
      if (!affiliate) {
        logger.warn(`Cannot find any affiliate with ID ${referredAffiliateId}`);
        continue;
      }

      const belongedCommissionGroup = affiliate.affiliateTier.commissionGroups.find((grp) =>
        grp.products.map((prd) => prd.id).includes(orderDetail.variant.productId)
      )
      if (belongedCommissionGroup) {
        const actualPrice = orderDetail.price * orderDetail.currentQuantity - orderDetail.discount;

        const monthString = format(new Date(), 'yyyy-MM');
        const key = `${monthString}_${belongedCommissionGroup.type}_${EAffiliateSnapshotKey.COMMISSION_RATE}`;
        const currentCommissionRate = await this.affiliationSnapshotsService.getSnapshotAsNumber(affiliate.id, key);

        const commissionAmount = actualPrice > 0 ? actualPrice * currentCommissionRate : 0

        const commissionDetail = {
          commissionAmount: commissionAmount,
          commissionRate: currentCommissionRate,
          orderDetailId: orderDetail.id,
        }

        if (affiliatesCommissionsMap.has(referredAffiliateId)) {
          affiliatesCommissionsMap.get(referredAffiliateId)?.push(commissionDetail);
          logger.info(`Added order detail ID ${orderDetail.id} to commission of affiliate ID ${referredAffiliateId}`);
        } else {
          logger.warn(`Commission map doesn't contains key of ${referredAffiliateId}`);
        }
      } else {
        logger.debug(`Product ID ${orderDetail.variant.productId} doesn't belong to any commission group`);
      }
    }

    for (const affiliateId of affiliatesCommissionsMap.keys()) {
      const commissionDetails = affiliatesCommissionsMap.get(affiliateId);
      if (!commissionDetails) continue;

      const totalCommissionAmount = commissionDetails.reduce((total, com) => total + com.commissionAmount, 0)

      const commission = await this.commissionService.createCommission(
        affiliateId,
        order.id,
        totalCommissionAmount
      )
      for (const commissionDetail of commissionDetails) {
        await this.commissionService.createCommissionDetail(
          commission.id,
          commissionDetail.orderDetailId,
          commissionDetail.commissionAmount,
          commissionDetail.commissionRate
        )
      }
    }
  }

  async verifyRegistrationStatus(affiliateId: string, data: any) {
    const payload = await approvalRegistrationValidator.validate(data)

    const registeringAffiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('user')
      .firstOrFail()

    if (registeringAffiliate.registerStatus == EApprovalStatus.APPROVED)
      throw new Error('Affiliate was already approved')

    if (payload.approval == EApprovalStatus.APPROVED) {
      const initialTier = await this.tierService.getStartingTier()
      registeringAffiliate.tierId = initialTier.id

      if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
        // const admins = await ZnAdmin.all()
        const admins = await this.adminNotificationService.getAdminsByPermissions([
          { action: ACTION.READ, resource: RESOURCE.AFFILIATION },
        ])
        admins.forEach(async (admin) => {
          await mail
            .send(
              new RegistrationApprovedNotification(
                registeringAffiliate.user.firstName,
                admin.username
              )
            )
            .then(() => {
              logger.info(
                `Registration approved email has been sent successfully to ${admin.username}`
              )
            })
            .catch((error) => {
              console.error('Error when sending email', error)
            })
        })
      } else {
        await mail
          .send(
            new RegistrationApprovedNotification(
              registeringAffiliate.user.firstName,
              registeringAffiliate.user.email
            )
          )
          .then(() => {
            logger.info(
              `Registration approved email has been sent successfully to ${registeringAffiliate.user.email}`
            )
          })
          .catch((error) => {
            console.error('Error when sending email', error)
          })

        await this.sendPushNotification(
          registeringAffiliate.user,
          registeringAffiliate.id,
          'Welcome Aboard!',
          'Your application is approved! Let earn commissions by sharing our products and services with your audience.'
        )
      }
    } else {
      if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
        // const admins = await ZnAdmin.all()
        const admins = await this.adminNotificationService.getAdminsByPermissions([
          { action: ACTION.READ, resource: RESOURCE.AFFILIATION },
        ])
        admins.forEach(async (admin) => {
          await mail
            .send(
              new RegistrationRejectNotification(
                registeringAffiliate.user.firstName,
                payload.note,
                admin.username
              )
            )
            .then(() => {
              logger.info(`Registration reject email has been sent successfully`)
            })
            .catch((error) => {
              console.error('Error when sending email', error)
            })
        })
      } else {
        await mail
          .send(
            new RegistrationRejectNotification(
              registeringAffiliate.user.firstName,
              payload.note,
              registeringAffiliate.user.email
            )
          )
          .then(() => {
            logger.info(`Registration reject email has been sent successfully`)
          })
          .catch((error) => {
            console.error('Error when sending email', error)
          })

        await this.sendPushNotification(
          registeringAffiliate.user,
          registeringAffiliate.id,
          'Application Update',
          'Sorry, your application was not approved. Contact support for details.'
        )
      }
    }

    registeringAffiliate.registerStatus = payload.approval
    registeringAffiliate.registerNote = payload.note

    return await registeringAffiliate.save()
  }

  async setRefCode(affiliateId: string, refCodeString: string) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('user')
      .preload('affiliateTier', (query) => {
        query.preload('discountCollection')
      })
      .preload('refCode')
      .firstOrFail()

    const isFirstRefCode = affiliate.refCode === null || affiliate.refCode === undefined;
    await this.refCodeService.createRefCode(affiliate, refCodeString);
    await affiliate.load('refCode');

    if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
      // const admins = await ZnAdmin.all()
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.AFFILIATION },
      ])
      admins.forEach(async (admin) => {
        await mail
          .send(
            new RefCodeAssignedNotification(
              affiliate.user.firstName,
              affiliate.refCode.code,
              isFirstRefCode,
              admin.username
            )
          )
          .then(() => {
            logger.info(`REF code email has been sent successfully to ${admin.username}`)
          })
          .catch((error) => {
            console.error('Error when sending email', error)
          })
      })
    } else {
      await mail
        .send(
          new RefCodeAssignedNotification(
            affiliate.user.firstName,
            affiliate.refCode.code,
            isFirstRefCode,
            affiliate.user.email
          )
        )
        .then(() => {
          logger.info(`REF code email has been sent successfully to ${affiliate.user.email}`)
        })
        .catch((error) => {
          console.error('Error when sending email', error)
        })

      await this.sendPushNotification(
        affiliate.user,
        affiliate.id,
        isFirstRefCode ? 'Your Referral Code Is Ready' : 'Your New Referral Code',
        isFirstRefCode
          ? 'Your new referral code has been assigned. Share it to start earning commissions.'
          : 'Your updated referral code is now active. Use it to earn commissions.'
      )
    }

    return affiliate
  }

  async updateTier(affiliateId: string, data: any) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('affiliateTier')
      .preload('refCode')
      .preload('user')
      .firstOrFail()

    if (!affiliate.affiliateTier) {
      throw new Error(`No tier is associated with affiliate ${affiliateId}`)
    }

    const tierAction = data.tierAction
    if (!tierAction) {
      throw new Error(`No tier action is specified`)
    }

    const tierQuery = ZnAffiliateTier.query()

    if (tierAction == ETierAction.UPGRADE) {
      tierQuery.where('tier', '>', affiliate.affiliateTier.tier).orderBy('tier', 'asc')
    } else if (tierAction == ETierAction.DOWNGRADE) {
      tierQuery.where('tier', '<', affiliate.affiliateTier.tier).orderBy('tier', 'desc')
    } else {
      throw new Error(`Tier action is unknown`)
    }

    const newTier = await tierQuery.firstOrFail()

    const isUpgrading = newTier.tier > affiliate.affiliateTier.tier;
    affiliate.tierId = newTier.id;
    await affiliate.save();

    await this.affiliationNotificationService.sendTierChangedNotification(affiliate, isUpgrading);

    return affiliate;
  }

  async payForCommissions(affiliateId: string, amount: number, paymentMethodId: string) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('user')
      .firstOrFail()

    if (!affiliate.balance || affiliate.balance == 0) {
      return {
        success: false,
        reason: 'Empty balance',
      }
    }

    if (amount > affiliate.balance) {
      return {
        success: false,
        reason: 'Not enough balance',
      }
    }

    const paymentMethod = await ZnPaymentMethod.findOrFail(paymentMethodId)

    const payment = await ZnAffiliateCommissionPayment.create({
      affiliateId: affiliate.id,
      paymentMethodId: paymentMethod.id,
      amount: amount,
    })

    if (
      affiliate.totalPayments !== null &&
      affiliate.totalPayments !== undefined &&
      !Number.isNaN(affiliate.totalPayments)
    )
      affiliate.totalPayments += payment.amount
    else affiliate.totalPayments = payment.amount
    await affiliate.save()
    if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
      // const admins = await ZnAdmin.all()
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.AFFILIATION },
      ])
      admins.forEach(async (admin) => {
        await mail
          .send(
            new PayoutNotification(
              affiliate.user.firstName,
              amount,
              paymentMethod.paymentTypeName,
              affiliate.balance,
              admin.username
            )
          )
          .then(() => {
            logger.info(`Payout email has been sent successfully to ${admin.username}`)
          })
          .catch((error) => {
            console.error('Error when sending email:', error)
          })
      })
    } else {
      await mail
        .send(
          new PayoutNotification(
            affiliate.user.firstName,
            amount,
            paymentMethod.paymentTypeName,
            affiliate.balance,
            affiliate.user.email
          )
        )
        .then(() => {
          logger.info(`Payout email has been sent successfully to ${affiliate.user.email}`)
        })
        .catch((error) => {
          console.error('Error when sending email:', error)
        })

      await this.sendPushNotification(
        affiliate.user,
        'payouts',
        'Payout Successful',
        `Your payout of $${amount} has been processed. Check your account for details.`
      )
    }

    return { success: true }
  }

  async previewSyncData(affiliateId: string) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('commissions', (query) => {
        query.preload('commissionDetails', (query) => {
          query.preload('orderDetail')
        })
      })
      .preload('commissionPayments')
      .firstOrFail()

    const updatedData = {
      itemsSold: 0,
      gmv: 0,
      refCodesUsed: 0,
      totalCommissionsAmount: 0,
      totalPayoutAmount: 0,
    }

    for (const commision of affiliate.commissions) {
      if (commision.status == EApprovalStatus.APPROVED) {
        for (const commissionDetail of commision.commissionDetails) {
          if (
            commissionDetail.orderDetail.currentQuantity > 0 &&
            commissionDetail.commissionRate > 0
          ) {
            updatedData.itemsSold += commissionDetail.orderDetail.currentQuantity
            updatedData.gmv +=
              commissionDetail.orderDetail.price * commissionDetail.orderDetail.currentQuantity -
              commissionDetail.orderDetail.discount
          }
        }
        updatedData.refCodesUsed += 1
        updatedData.totalCommissionsAmount += commision.finalAmount
      }
    }

    updatedData.totalPayoutAmount = affiliate.commissionPayments
      .map((payout) => payout.amount)
      .reduce((total, amount) => total + amount, 0)

    return {
      itemsSold: {
        current: affiliate.itemSold,
        new: updatedData.itemsSold,
      },
      gmv: {
        current: affiliate.grossMerchandiseValue,
        new: updatedData.gmv,
      },
      refCodesUsed: {
        current: affiliate.refCodeUsed,
        new: updatedData.refCodesUsed,
      },
      commissionAmount: {
        current: affiliate.totalCommissions,
        new: updatedData.totalCommissionsAmount,
      },
      payoutAmount: {
        current: affiliate.totalPayments,
        new: updatedData.totalPayoutAmount,
      },
    }
  }

  async proceedSyncData(
    affiliateId: string,
    { itemsSold, gmv, refCodesUsed, totalCommissionsAmount, totalPayoutAmount }: any
  ) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('commissions')
      .preload('commissionPayments')
      .firstOrFail()

    affiliate.itemSold = itemsSold
    affiliate.grossMerchandiseValue = gmv
    affiliate.refCodeUsed = refCodesUsed
    affiliate.totalCommissions = totalCommissionsAmount
    affiliate.totalPayments = totalPayoutAmount
    await affiliate.save()

    return affiliate
  }

  async delete(affiliateId: string) {
    const affiliate = await ZnAffiliate.find(affiliateId)
    if (!affiliate) return
    await this.refCodeService.delete(affiliate.refCodeId)
    await affiliate.softDelete()
  }

  private async sendPushNotification(
    user: ZnUser,
    resourceId: string,
    title: string,
    description: string
  ) {
    const notification = await Notification.create({
      type: NOTIFICATION_TYPE.AFFILIATE,
      userId: user.id,
      resourceId: resourceId,
      title: title,
      description: description,
    })

    if (notification) {
      const notificationService = new NotificationService()
      await notificationService.send([user], [notification])
    }
  }

  async getCommissionAndDiscountPercentage({
    userId,
    productId,
  }: {
    userId: string
    productId: string
  }) {
    const affiliate = await ZnAffiliate.query()
      .where('userId', userId)
      .preload('affiliateTier', (query) => {
        query.preload('commissionGroups', (query) => {
          query.preload('products')
        })
        query.preload('discountCollection', (query) => {
          query.preload('products')
        })
      })
      .first()

    if (!affiliate) {
      return {
        commissionPercent: 0,
        discountPercent: 0,
      }
    }

    let commissionPercent = 0
    for (const group of affiliate.affiliateTier.commissionGroups) {
      const productIsInCommissionGroup = group.products
        .map((prd) => prd.id)
        .some((id) => id === productId)
      if (productIsInCommissionGroup) {
        commissionPercent = group.commissionRate
        break
      }
    }

    const productIsInDiscountCollection = affiliate.affiliateTier.discountCollection?.products.some(
      (product) => product.id === productId
    )
    const discountPercent = productIsInDiscountCollection
      ? affiliate.affiliateTier.defaultDiscount
      : 0

    return {
      commissionPercent: commissionPercent,
      discountPercent: discountPercent,
    }
  }

  async getVideoReport(userId: string, range: IDateRange): Promise<IVideoReportData> {
    let fromDate = range.from
    let toDate = range.to

    if (!fromDate && !toDate) {
      fromDate = startOfMonth(new Date())
      toDate = addDays(endOfMonth(new Date()), 1)
    } else {
      if (toDate) {
        toDate = addDays(toDate, 1)
      }
    }

    const videoCount = await this.getVideoCount(userId, fromDate, toDate)
    const totalViews = await this.getTotalViews(userId, fromDate, toDate)
    const totalProducts = await this.getTotalProducts(userId, fromDate, toDate)
    const itemsSold = await this.getItemsSold(userId, fromDate, toDate)

    return {
      videoCount,
      totalViews,
      totalProducts,
      itemsSold,
    }
  }

  async getMostViewedVideosForAffiliate(userId: string, range: IDateRange) {
    const postQuery = ZnPost.query()
      .where('userId', userId)
      .whereHas('medias', (mediaQuery) => {
        mediaQuery.where('type', MEDIA_TYPE.VIDEO)
      })
      .where((videoInquiry) => {
        videoInquiry.orWhereIn('type', [EPostType.VIDEO]).orWhere((postTypeQuery) => {
          postTypeQuery.whereNull('type').whereHas('medias', (mediaQuery) => {
            mediaQuery.where('type', MEDIA_TYPE.VIDEO)
          })
        })
      })
      .whereNull('deletedAt')
      .where('expired', false)
      .where('isDraft', false)
      .where('isUnlist', false)
      .preload('thumbnail')
      .preload('timelines', (query) => {
        query.whereNull('deletedAt')
      })
      .orderBy('createdAt', 'desc')

    if (range.from) postQuery.where('createdAt', '>=', range.from)
    if (range.to) postQuery.where('createdAt', '<', addDays(range.to, 1))

    const posts = await postQuery

    if (!posts.length) return []

    const affiliate = await ZnAffiliate.findByOrFail('userId', userId)

    const postIds = posts.map((p) => p.id)
    const viewCounts = await ZnResourceInteracts.query()
      .whereIn('resourceId', postIds)
      .where('resource', 'ZnPost')
    const viewCountMap = Object.fromEntries(viewCounts.map((vc) => [vc.resourceId, vc.viewCount]))

    const allVariantIds = posts.flatMap((post) => post.timelines.map((tl) => tl.variantId))
    const uniqueVariantIds = Array.from(new Set(allVariantIds))

    let soldItemsMap: Record<string, number> = {}
    let commissionMap: Record<string, number> = {}

    if (uniqueVariantIds.length) {
      let soldItemsQuery = `
        SELECT vt.postId, SUM(od.currentQuantity) as totalSold
        FROM zn_order_details od
        INNER JOIN zn_orders o ON od.orderId = o.id
        INNER JOIN zn_video_timelines vt ON od.variantId = vt.variantId
        WHERE vt.postId IN (${postIds.map(() => '?').join(',')})
          AND od.variantId IN (${uniqueVariantIds.map(() => '?').join(',')})
          AND o.financialStatus = 'paid'
          AND od.deletedAt IS NULL
          AND o.deletedAt IS NULL
          AND vt.deletedAt IS NULL`

      let soldItemsParams = [...postIds, ...uniqueVariantIds]

      if (range.from) {
        soldItemsQuery += ` AND o.createdAt >= ?`
        soldItemsParams.push(formatDBDate(range.from))
      }
      if (range.to) {
        soldItemsQuery += ` AND o.createdAt < ?`
        soldItemsParams.push(formatDBDate(addDays(range.to, 1)))
      }

      soldItemsQuery += ` GROUP BY vt.postId`

      const soldItemsRows = await db.rawQuery(soldItemsQuery, soldItemsParams)
      soldItemsMap = Object.fromEntries(
        (soldItemsRows[0] as any[]).map((row: any) => [row.postId, Number(row.totalSold) || 0])
      )

      let commissionQuery = `
        SELECT vt.postId,
               SUM(CASE
                 WHEN ac.adjustedAmount IS NOT NULL AND ac.adjustedAmount > 0
                 THEN ac.adjustedAmount
                 ELSE ac.commissionAmount
               END) as totalCommission
        FROM zn_affiliate_commissions ac
        INNER JOIN zn_orders o ON ac.orderId = o.id
        INNER JOIN zn_order_details od ON od.orderId = o.id
        INNER JOIN zn_video_timelines vt ON od.variantId = vt.variantId
        WHERE vt.postId IN (${postIds.map(() => '?').join(',')})
          AND od.variantId IN (${uniqueVariantIds.map(() => '?').join(',')})
          AND ac.affiliateId = ?
          AND o.financialStatus = 'paid'
          AND ac.deletedAt IS NULL
          AND o.deletedAt IS NULL
          AND vt.deletedAt IS NULL
          AND ac.status != 'rejected'`

      let commissionParams = [...postIds, ...uniqueVariantIds, affiliate.id]

      if (range.from) {
        commissionQuery += ` AND ac.createdAt >= ?`
        commissionParams.push(formatDBDate(range.from))
      }
      if (range.to) {
        commissionQuery += ` AND ac.createdAt < ?`
        commissionParams.push(formatDBDate(addDays(range.to, 1)))
      }

      commissionQuery += ` GROUP BY vt.postId`

      const commissionRows = await db.rawQuery(commissionQuery, commissionParams)
      commissionMap = Object.fromEntries(
        (commissionRows[0] as any[]).map((row: any) => [
          row.postId,
          Number(row.totalCommission) || 0,
        ])
      )
    }

    const ADS_WEBSITE_DOMAIN = process.env.ADS_WEBSITE_DOMAIN || ''

    return posts.map((post) => ({
      id: post.id,
      uploadDate: post.createdAt,
      name: post.title,
      thumbnail: post.thumbnail ? post.thumbnail.serialize() : null,
      url: `${ADS_WEBSITE_DOMAIN}/post/${post.id}`,
      totalViews: viewCountMap[post.id] || 0,
      totalProductsAttached: post.timelines.length,
      totalSoldItems: soldItemsMap[post.id] || 0,
      totalCommission: commissionMap[post.id] || 0,
    }))
  }

  private async getVideoCount(
    userId: string,
    fromDate: Date | null,
    toDate: Date | null
  ): Promise<number> {
    const query = ZnPost.query()
      .where('userId', userId)
      .whereHas('medias', (mediaQuery) => {
        mediaQuery.where('type', MEDIA_TYPE.VIDEO)
      })
      .where((videoInquiry) => {
        videoInquiry.orWhereIn('type', [EPostType.VIDEO]).orWhere((postTypeQuery) => {
          postTypeQuery.whereNull('type').whereHas('medias', (mediaQuery) => {
            mediaQuery.where('type', MEDIA_TYPE.VIDEO)
          })
        })
      })
      .whereNull('deletedAt')
      .where('expired', false)
      .where('isDraft', false)
      .where('isUnlist', false)

    if (fromDate) {
      query.where('createdAt', '>=', fromDate)
    }
    if (toDate) {
      query.where('createdAt', '<', toDate)
    }

    const result = await query.count('* as total')
    return Number(result[0].$extras.total) || 0
  }

  private async getTotalViews(
    userId: string,
    fromDate: Date | null,
    toDate: Date | null
  ): Promise<number> {
    const userVideoPosts = await ZnPost.query()
      .where('userId', userId)
      .whereHas('medias', (mediaQuery) => {
        mediaQuery.where('type', MEDIA_TYPE.VIDEO)
      })
      .where((videoInquiry) => {
        videoInquiry.orWhereIn('type', [EPostType.VIDEO]).orWhere((postTypeQuery) => {
          postTypeQuery.whereNull('type').whereHas('medias', (mediaQuery) => {
            mediaQuery.where('type', MEDIA_TYPE.VIDEO)
          })
        })
      })
      .whereNull('deletedAt')
      .where('expired', false)
      .where('isDraft', false)
      .where('isUnlist', false)
      .select('id')

    if (userVideoPosts.length === 0) {
      return 0
    }

    const postIds = userVideoPosts.map((post) => post.id)

    const query = ZnTracking.query()
      .whereIn('resourceId', postIds)
      .where('action', TRACKING_ACTION.VIEW_POST)
      .where('resource', 'ZnPost')

    if (fromDate) {
      query.where('createdAt', '>=', fromDate)
    }
    if (toDate) {
      query.where('createdAt', '<', toDate)
    }

    const result = await query.count('* as total')
    return Number(result[0].$extras.total) || 0
  }

  private async getTotalProducts(
    userId: string,
    fromDate: Date | null,
    toDate: Date | null
  ): Promise<number> {
    let dateCondition = ''
    const params: any[] = [userId, EPostType.VIDEO]

    if (fromDate && toDate) {
      dateCondition = 'AND vt.createdAt >= ? AND vt.createdAt < ?'
      params.push(fromDate, toDate)
    } else if (fromDate) {
      dateCondition = 'AND vt.createdAt >= ?'
      params.push(fromDate)
    } else if (toDate) {
      dateCondition = 'AND vt.createdAt < ?'
      params.push(toDate)
    }

    const query = `
      SELECT COUNT(DISTINCT pv.productId) as total
      FROM zn_video_timelines vt
      INNER JOIN zn_posts p ON vt.postId = p.id
      INNER JOIN zn_product_variants pv ON vt.variantId = pv.id
      WHERE p.userId = ?
        AND (
          p.type = ?
          OR (
            p.type IS NULL
            AND EXISTS (
              SELECT 1 FROM zn_posts_medias pm
              INNER JOIN zn_medias m ON pm.mediaId = m.id
              WHERE pm.postId = p.id AND m.type = 'video'
            )
          )
        )
        AND p.deletedAt IS NULL
        AND p.expired = false
        AND p.isDraft = false
        AND p.isUnlist = false
        AND vt.deletedAt IS NULL
        AND pv.deletedAt IS NULL
        AND EXISTS (
          SELECT 1 FROM zn_posts_medias pm2
          INNER JOIN zn_medias m2 ON pm2.mediaId = m2.id
          WHERE pm2.postId = p.id AND m2.type = 'video'
        )
        ${dateCondition}
    `

    const result = await db.rawQuery(query, params)
    return Number(result[0][0]?.total) || 0
  }

  private async getItemsSold(
    userId: string,
    fromDate: Date | null,
    toDate: Date | null
  ): Promise<number> {
    let dateCondition = ''
    const params: any[] = [userId, EPostType.VIDEO]

    if (fromDate && toDate) {
      dateCondition = 'AND od.createdAt >= ? AND od.createdAt < ?'
      params.push(fromDate, toDate)
    } else if (fromDate) {
      dateCondition = 'AND od.createdAt >= ?'
      params.push(fromDate)
    } else if (toDate) {
      dateCondition = 'AND od.createdAt < ?'
      params.push(toDate)
    }

    const query = `
      SELECT SUM(od.currentQuantity) as total
      FROM zn_order_details od
      INNER JOIN zn_orders o ON od.orderId = o.id
      INNER JOIN zn_video_timelines vt ON od.variantId = vt.variantId
      INNER JOIN zn_posts p ON vt.postId = p.id
      WHERE p.userId = ?
        AND (
          p.type = ?
          OR (
            p.type IS NULL
            AND EXISTS (
              SELECT 1 FROM zn_posts_medias pm
              INNER JOIN zn_medias m ON pm.mediaId = m.id
              WHERE pm.postId = p.id AND m.type = 'video'
            )
          )
        )
        AND p.deletedAt IS NULL
        AND p.expired = false
        AND p.isDraft = false
        AND p.isUnlist = false
        AND vt.deletedAt IS NULL
        AND od.deletedAt IS NULL
        AND o.deletedAt IS NULL
        AND o.financialStatus = 'paid'
        AND EXISTS (
          SELECT 1 FROM zn_posts_medias pm2
          INNER JOIN zn_medias m2 ON pm2.mediaId = m2.id
          WHERE pm2.postId = p.id AND m2.type = 'video'
        )
        ${dateCondition}
    `

    const result = await db.rawQuery(query, params)
    return Number(result[0][0]?.total) || 0
  }

  async recordNewCustomer(shopifyOrderId: string) {
    const order = await ZnOrder.query()
      .where('shopifyId', shopifyOrderId)
      .preload('orderDiscounts')
      .preload('user')
      .first();

    if (!order) {
      logger.debug('Checking new customer. No order found');
      return;
    }

    if (order.financialStatus !== 'paid') {
      logger.debug("Checking new customer. The order isn't paid yet.");
      return;
    }

    if (!order.orderDiscounts || order.orderDiscounts.length === 0) {
      logger.debug("Checking new customer. The order isn't applied any discount code.");
      return;
    }

    const firstPaidOrder = await ZnOrder.query()
      .where('customerId', order.customerId)
      .where('financialStatus', 'paid')
      .orderBy('createdAt', 'asc')
      .first();
    if (!firstPaidOrder) {
      logger.debug("Checking new customer. This customer doesn't pay any order.");
      return;
    }

    if (order.id !== firstPaidOrder.id) {
      logger.debug("Checking new customer. This order is not the first paid order, not a new customer.");
      return;
    }

    for (const orderDiscount of order.orderDiscounts) {
      console.log('discountCode:', orderDiscount.discountCode);
      const affiliate = await ZnAffiliate.query()
        .whereHas('refCode', (query) => {
          query.where('code', orderDiscount.discountCode);
        })
        .preload('user')
        .first();

      if (!affiliate) {
        logger.debug(`Checking new customer. No affiliate associates with discount code ${orderDiscount.discountCode}.`);
        continue;
      }

      if (!order.user) {
        logger.debug(`Checking new customer. Cannot find customer of order.`);
        continue;
      }

      await affiliate.related('referredCustomers').attach([order.user.id]);
      logger.info(`Checking new customer. Affiliate ${affiliate.user.fullname} has referred user ${order.user.fullname} successfully.`);
    }
  }

  async removeNewCustomer(shopifyOrderId: string) {
    const order = await ZnOrder.query()
      .where('shopifyId', shopifyOrderId)
      .first();

    if (!order) return;
  }

  private getStartDateOfMonth(date: Date) {
    return new Date(date.getFullYear(), date.getMonth(), 1);
  }

  private getEndDateOfMonth(date: Date) {
    return new Date(date.getFullYear(), date.getMonth() + 1, 1);
  }
}
