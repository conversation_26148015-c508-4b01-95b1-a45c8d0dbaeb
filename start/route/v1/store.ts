/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const StoreController = () => import('#controllers/store_controller')

export default function storeRoutes() {
  router
    .group(() => {
      router
        .group(() => {
          router.get('/mine', [StoreController, 'getMyStore'])

          router.post('/', [StoreController, 'create'])
          router.put('/:id', [StoreController, 'update'])
          router.delete('/:id', [StoreController, 'softDelete'])

          router.post('/claim', [StoreController, 'claimStore'])
          router.post('/verify', [StoreController, 'verifyStore'])
          router.post('/sync-instagram', [StoreController, 'syncInstagram'])
          router.post('/enable-manage-booking', [StoreController, 'enableManageBooking'])
          router.post('/disable-manage-booking', [StoreController, 'disableManageBooking'])
          router.get('/booking-summary', [StoreController, 'getTotalBookingsSummary'])
        })
        .use(middleware.auth({ guards: ['jwt_admin', 'jwt_user'] as any }))

      router.get('/', [StoreController, 'list']).use(middleware.trackSearch())
      router.get('/popular', [StoreController, 'popular'])
      router.get('/images/:id', [StoreController, 'getAllStoreImages'])
      router
        .get('/:storeId/bookings', [StoreController, 'getStoreBookings'])
        .use(middleware.auth({ guards: ['jwt_admin', 'jwt_user'] as any }))
      router.get('/:id', [StoreController, 'show'])
    })
    .prefix('store')
}
