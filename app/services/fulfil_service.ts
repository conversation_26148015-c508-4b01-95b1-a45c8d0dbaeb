import env from "#start/env";
import axios from "axios";
import { BigQueryService } from "./google/big_query_service.js";

export class FulfilService {
  private baseUrl = env.get('FULFIL_API_BASE_URL')
  private token = env.get('FULFIL_API_KEY')
  private templateId = env.get('FULFIL_VENDOR_TEMPLATE') || '18'

  private get headers() {
    return {
      'Content-Type': 'application/json',
      'X-API-KEY': `${this.token}`,
    }
  }

  private async rpcCall(model: string, params: any) {
    const response = await axios.post(
      `${this.baseUrl}/model/${model}`,
      params,
      { headers: this.headers }
    )
    return response.data
  }

  private async getModel(model: string, params: any) {
    const response = await axios.get(
      `${this.baseUrl}/model/${model}`,
      {
        params,
        headers: this.headers
      }
    )
    return response.data
  }
  /**
   * Create or get a product.brand
   */
  public async getOrCreateBrand(brand: { name: string }) {
    const brandExist = await this.getModel('product.brand', brand)
    if (brandExist) {
      return brandExist[0]
    }
    const created = await this.rpcCall('product.brand', [brand])
    return created[0]
  }

  /**
   * Create product.template
   */
  public async createProductTemplate(template: {
    name: string
    description?: string
    list_price?: number
  }) {
    const result = await this.rpcCall('product.template', [template])
    return result[0]
  }

  /**
   * Create product.product with brand
   */
  public async createProduct(product: {
    template: number
    name: string,
    description: string,
    brand: number
    code?: string
    list_price?: string
    cost_price?: string
  }) {
    const result = await this.rpcCall('product.product', [
      {
        template: this.templateId,
        brand: product.brand,
        variant_name: product.name,
        code: product.code,
        description: product.description,
        list_price: product.list_price,
        cost_price: product.cost_price,
        cost_price_method: "average",
        default_uom: 1,
        type: "goods"
      }
    ])
    return result[0]
  }

  /**
   * Push product with brand (on product.product)
   */
  public async pushProductWithBrand(data: {
    name: string
    description: string
    code: string
    list_price?: string
    cost_price?: string
    vendor: { name: string }
  }) {
    const brandId = await this.getOrCreateBrand(data.vendor)

    // const templateId = await this.createProductTemplate({
    //   name: data.templateName,
    //   description: data.description,
    //   list_price: data.list_price,
    // })

    return await this.createProduct({
      template: 18,
      name: data.name,
      description: data.description,
      brand: brandId?.id,
      code: data.code,
      list_price: data.list_price,
      cost_price: data.cost_price,
    })
  }

  /**
   * Get Product From Code (from product.product)
   */
  public async getProductFromCode(data: {
    code: string
  }) {
    const list = await this.getModel(
      'product.product',
      { code: data.code },
    )

    return list[0]
  }

  async getProductFromId(id: string) {

    const query = `
        SELECT *
        FROM \`${env.get('FUFIL_DATAWAREHOUSE_APPLICATION_ID')}.products\`
        WHERE id = ${id}
      `
    const bigQueryService = new BigQueryService()
    const data = await bigQueryService.runQuery(query)

    return data[0]
  }
}
