import SyncInstagramMediaToStoreJob from '#jobs/sync_instagram_media_to_store_job'
import ZnClaimStore, { EClaimStore } from '#models/zn_claim_store'
import ZnMedia from '#models/zn_media'
import ZnPost from '#models/zn_post'
import Store from '#models/zn_store'
import ZnUser from '#models/zn_user'
import {
  claimStoreValidator,
  createStoreValidator,
  syncInstagramValidator,
} from '#validators/store_validator'
import { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import queue from '@rlanz/bull-queue/services/main'
import { DateTime } from 'luxon'
import { getRandomArbitrary } from '../../services/commons.js'
import { StoreService } from '#services/store_service'
import ZnStore from '#models/zn_store'
import VoiceOtpService from '#services/aws/voice_otp_service'
import { SMSService } from '#services/aws/sms_service'
import { GooglePlaceService } from '#services/google/google_place_service'
import ZnCountry from '#models/zn_country'
import ZnState from '#models/zn_state'
import ZnCity from '#models/zn_city'
import ZnResourceFavorite from '#models/zn_resource_favorite'
import JwtService from '#services/jwt_service'
import ZnAppointment from '#models/store_service/zn_appointment'

export default class StoreController {
  private smsService: SMSService
  private voiceService: VoiceOtpService
  private storeService: StoreService
  private googlePlaceService: GooglePlaceService

  constructor() {
    this.smsService = new SMSService()
    this.voiceService = new VoiceOtpService()
    this.storeService = new StoreService()
    this.googlePlaceService = new GooglePlaceService()
  }

  /**
   * @create
   * @tag Store
   */
  async create({ request, response, auth }: HttpContext) {
    // @ts-ignore
    const user = (await auth.getUserOrFail().serialize()) as ZnUser
    const data = request.all()

    try {
      // const payload = await createStoreValidator(undefined, user.id).validate(data)
      const payload = await createStoreValidator().validate(data)

      const existingStoreWithPhone = payload.phoneNumber
        ? await Store.query().where('phoneNumber', payload.phoneNumber).first()
        : null

      const existingStoreWithPlaceId = payload.placeId
        ? await Store.query().where('placeId', payload.placeId).first()
        : null

      if (existingStoreWithPhone && existingStoreWithPhone.userId) {
        return response.badRequest({
          message: 'Validation failed',
          errors: [
            {
              message: 'The phoneNumber has already been taken',
              rule: 'database.unique',
              field: 'phoneNumber',
              store: existingStoreWithPhone,
            },
          ],
        })
      }

      if (existingStoreWithPlaceId && existingStoreWithPlaceId.userId) {
        return response.badRequest({
          message: 'Validation failed',
          errors: [
            {
              message: 'The placeId has already been taken',
              rule: 'database.unique',
              field: 'placeId',
              store: existingStoreWithPlaceId,
            },
          ],
        })
      }

      if (existingStoreWithPhone && !existingStoreWithPhone.userId) {
        existingStoreWithPhone.merge({ userId: user.id })
        await existingStoreWithPhone.save()
        return response.ok(existingStoreWithPhone)
      }

      if (existingStoreWithPlaceId && !existingStoreWithPlaceId.userId) {
        existingStoreWithPlaceId.merge({ userId: user.id })
        await existingStoreWithPlaceId.save()
        return response.ok(existingStoreWithPlaceId)
      }

      let workingHour = payload.workingHour
      if (typeof workingHour === 'object') {
        workingHour = JSON.stringify(workingHour) as any
      }

      let countryId, stateId, cityId
      if (payload.placeId) {
        const placeDetails = await this.googlePlaceService.getPlaceDetails(payload.placeId)

        const country = await ZnCountry.updateOrCreate(
          { name: placeDetails.country },
          { name: placeDetails.country }
        )
        countryId = country.id

        const state = await ZnState.updateOrCreate(
          { name: placeDetails.state, countryId: country.id },
          { name: placeDetails.state, countryId: country.id }
        )
        stateId = state.id

        const city = await ZnCity.updateOrCreate(
          { name: placeDetails.city, stateId: state.id },
          { name: placeDetails.city, stateId: state.id }
        )
        cityId = city.id
      }

      const created = await Store.create({
        userId: user.id,
        name: payload.name,
        latitude: payload.latitude,
        longitude: payload.longitude,
        address: payload.address,
        phoneNumber: payload.phoneNumber,
        website: payload.website,
        workingHour: workingHour,
        thumbnailId: payload.thumbnailId ?? undefined,
        logoId: payload.logoId ?? undefined,
        zipCode: payload.zipCode,
        socials: payload.socials,
        timezone: payload.timezone,
        placeId: payload.placeId,
        countryId,
        stateId,
        cityId,
      })

      if (payload.mediaIds) {
        await created.related('medias').sync(payload.mediaIds)
      }

      if (payload.nailSystemIds && payload.nailSystemIds.length > 0) {
        await created.related('nailSystems').sync(payload.nailSystemIds)
      }

      return response.created(created)
    } catch (error) {
      console.log('Error:', error)
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }
      return response.internalServerError({
        message: error.message || 'Something went wrong',
      })
    }
  }

  /**
   * @update
   * @tag Store
   */
  async update({ request, response, params, auth }: HttpContext) {
    const storeId = params.id

    const store = await Store.query().where('id', storeId).first()

    // @ts-ignore
    const user = auth.getUserOrFail().serialize() as ZnUser

    if (!store) {
      return response.notFound({ message: 'Store not found' })
    }

    try {
      const data = request.all()

      // const payload = await createStoreValidator(storeId, user.id).validate(data)
      const payload = await createStoreValidator().validate(data)

      if (payload.placeId) {
        const existingStore = await ZnStore.query()
          .where('placeId', payload.placeId)
          .whereNot('id', storeId)
          .first()

        if (existingStore) {
          return response.badRequest({
            message: 'Validation failed',
            errors: [
              {
                message: 'The placeId has already been taken',
                rule: 'database.unique',
                field: 'placeId',
                store: existingStore,
              },
            ],
          })
        }
      }

      store.name = payload.name
      store.latitude = payload.latitude || store.latitude
      store.longitude = payload.longitude || store.longitude
      store.address = payload.address
      store.website = payload.website || store.website
      store.phoneNumber = payload.phoneNumber || store.phoneNumber
      store.workingHour = payload.workingHour || store.workingHour
      store.thumbnailId = payload.thumbnailId || store.thumbnailId
      store.logoId = payload.logoId || null
      store.zipCode = payload.zipCode || store.zipCode
      store.socials = payload.socials || store.socials
      store.updatedAt = DateTime.local()
      store.placeId = payload.placeId || store.placeId
      store.timezone = payload.timezone || store.timezone

      if (payload.mediaIds) {
        await store.related('medias').sync(payload.mediaIds)
      }

      if (payload.nailSystemIds && payload.nailSystemIds.length > 0) {
        await store.related('nailSystems').sync(payload.nailSystemIds)
      }

      if (payload.placeId) {
        const placeDetails = await this.googlePlaceService.getPlaceDetails(payload.placeId)

        const country = await ZnCountry.updateOrCreate(
          { name: placeDetails.country },
          { name: placeDetails.country }
        )
        store.countryId = country.id

        const state = await ZnState.updateOrCreate(
          { name: placeDetails.state, countryId: country.id },
          { name: placeDetails.state, countryId: country.id }
        )
        store.stateId = state.id

        const city = await ZnCity.updateOrCreate(
          { name: placeDetails.city, stateId: state.id },
          { name: placeDetails.city, stateId: state.id }
        )
        store.cityId = city.id
      }

      const updated = await store.save()

      return response.ok(updated)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @list
   * @tag Store
   */
  async list({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10, search, latitude, longitude, mile } = request.qs()

      const query = Store.query()
        .preload('logo')
        .preload('medias')
        .preload('posts')
        .preload('nailSystems')
        .withCount('posts', (query) => {
          query.where('isDraft', 0).whereNull('deletedAt')
        })

      if (latitude && longitude && mile) {
        const lat = parseFloat(latitude as string)
        const lng = parseFloat(longitude as string)
        const radiusMiles = parseFloat(mile as string)

        if (isNaN(lat) || isNaN(lng) || isNaN(radiusMiles)) {
          return response.badRequest({
            message: 'Invalid coordinates or distance provided',
            errors: [
              {
                message: 'latitude, longitude, and mile must be valid numbers',
                rule: 'validation',
                field: 'coordinates',
              },
            ],
          })
        }

        query.select(
          db.raw(
            `
            (3959 * acos(
              cos(radians(?)) * 
              cos(radians(latitude)) * 
              cos(radians(longitude) - radians(?)) + 
              sin(radians(?)) * 
              sin(radians(latitude))
            )) as distance
          `,
            [lat, lng, lat]
          )
        )

        query.whereRaw(
          `
          (3959 * acos(
            cos(radians(?)) * 
            cos(radians(latitude)) * 
            cos(radians(longitude) - radians(?)) + 
            sin(radians(?)) * 
            sin(radians(latitude))
          )) <= ?
        `,
          [lat, lng, lat, radiusMiles]
        )

        query.orderByRaw('distance ASC')
      }

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(address) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(phoneNumber) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(website) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      const result = await query.paginate(page, limit)

      return response.ok(result.serialize())
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @show
   * @tag Store
   */
  async show({ params, response, auth }: HttpContext) {
    try {
      const storeId = params.id

      const store = await Store.query()
        .preload('logo')
        .preload('medias')
        .preload('posts', (postQuery) => {
          postQuery.where('isDraft', 0).preload('categories', (categoryQuery) => {
            categoryQuery.preload('thumbnail')
          })
          postQuery.preload('thumbnail')
        })
        .preload('nailSystems')
        .preload('claims')
        .withCount('posts', (query) => {
          query.where('isDraft', 0).whereNull('deletedAt')
        })
        .where('id', storeId)
        .first()
      const currentUser = (await auth.check())
        ? // @ts-ignore
          (auth?.getUserOrFail()?.serialize() as ZnUser)
        : null

      return response.ok({
        ...store?.serialize(),
        mine: store?.userId === currentUser?.id,
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @softDelete
   * @tag Store
   */
  async softDelete({ params, response }: HttpContext) {
    const storeId = params.id

    const store = await Store.query().where('id', storeId).first()

    if (!store) {
      return response.notFound({ message: 'Store not found' })
    }

    try {
      await store.softDelete()

      return response.ok({ message: 'Store soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @getMyStore
   * @tag Store
   */
  async getMyStore({ request, response, auth }: HttpContext) {
    // @ts-ignore
    const user = auth.getUserOrFail().serialize() as ZnUser

    try {
      const { page = 1, limit = 10, includeBookingCount = false, startDate, endDate } = request.qs()

      const query = Store.query()
        .preload('logo')
        .preload('medias')
        .preload('posts')
        .preload('nailSystems')
        .where('userId', user.id)
        .whereNull('deletedAt')

      // Add booking count if requested
      if (includeBookingCount) {
        let appointmentCountQuery = db
          .from('zn_appointments')
          .whereRaw('zn_appointments.storeId = zn_stores.id')
          .whereIn('status', ['booked', 'confirmed', 'checked-in', 'in-service', 'completed'])
          .whereNull('deletedAt')
          .count('* as count')

        if (startDate) {
          appointmentCountQuery.where('createdAt', '>=', DateTime.fromISO(startDate).toJSDate())
        }
        if (endDate) {
          appointmentCountQuery.where(
            'createdAt',
            '<=',
            DateTime.fromISO(endDate).endOf('day').toJSDate()
          )
        }

        query
          .withCount('posts')
          .select(db.raw(`zn_stores.*, (${appointmentCountQuery.toQuery()}) as booked_count`))
      } else {
        query.withCount('posts')
      }

      const result = await query.paginate(page, limit)

      if (includeBookingCount) {
        const serialized = result.serialize()
        serialized.data = serialized.data.map((store: any) => ({
          ...store,
          bookedCount: Number(store.booked_count || 0),
        }))
        return response.ok(serialized)
      }

      return response.ok(result.serialize())
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @claimStore
   * @tag Store
   * @summary Claim store
   * @description Claim store descriptively
   * @requestBody {"storeId":"","type":"email|sms|voice"}
   * @responseBody 200 - <ZnClaimStore> - Ok
   */
  async claimStore({ request, response, auth }: HttpContext) {
    // @ts-ignore
    const user = auth.getUserOrFail().serialize() as ZnUser

    try {
      const data = request.all()
      const payload = await claimStoreValidator.validate({
        ...data,
        // set default claim type to email
        type: data.type || 'email',
      })

      const store = await Store.query()
        .where('id', payload.storeId)
        .preload('country')
        .preload('state')
        .preload('city')
        .first()

      if (!store) {
        return response.notFound({ message: 'Store not found!' })
      }

      if (store?.verified) {
        return response.unprocessableEntity({ message: 'This store has been verified!' })
      }

      const createdRequest = await ZnClaimStore.updateOrCreate(
        { userId: user.id, storeId: store?.id, type: payload.type },
        {
          userId: user.id,
          storeId: store?.id,
          status: EClaimStore.PENDING,
          type: payload.type,
        }
      )

      if (payload?.type && store?.phoneNumber && ['voice', 'sms'].includes(payload.type)) {
        //Send OTP
        const otp = getRandomArbitrary(111111, 999999)

        if (payload.type == 'sms') {
          const message = `Your OTP for verifying ownership of ${store.name} in the app is ${otp}.
              Enter this code in the Zurno app within 15 minutes to complete the verification.`

          await this.smsService.send(store.phoneNumber, message)
        } else if (payload.type == 'voice') {
          const otpMessage = `<prosody rate='-50%'><say-as interpret-as="digits">${otp}</say-as></prosody>`
          const message = `Your OTP for verifying ownership of your store in the app is ${otpMessage}.
              Enter this code in the Zurno app within 15 minutes to complete the verification. Again the number is ${otpMessage}`

          await this.voiceService.sendVoiceOtp(message, store.phoneNumber)
        }

        //Update claim store
        createdRequest.otp = otp
        createdRequest.codeExpiredAt = DateTime.now().plus({ minutes: 15 })
        await createdRequest.save()
      } else {
        await this.storeService.sendClaimRequestEmail(user, store)
      }

      return response.ok(createdRequest)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @getAllStoreImages
   * @tag Store
   */
  async getAllStoreImages({ response, params, request }: HttpContext) {
    const storeId = params.id

    const { page = 1, limit = 10 } = request.qs()

    try {
      const allStorePostIds = await ZnPost.query()
        .where('storeId', storeId)
        .select(['id', 'thumbnailId'])
      const postIds = allStorePostIds.map((post) => post.id)

      const postMediaIds = await db
        .from('zn_posts_medias')
        .whereIn('postId', postIds)
        .select('mediaId')

      const storeMediaIds = await db
        .from('zn_stores_medias')
        .where('storeId', storeId)
        .select('mediaId')

      const thumbnailIds = allStorePostIds
        .filter((post) => post.thumbnailId)
        .map((post) => post.thumbnailId)

      const imageIds = [
        ...new Set([
          ...postMediaIds.map((p) => p.mediaId),
          ...storeMediaIds.map((p) => p.mediaId),
          ...thumbnailIds,
        ]),
      ]

      const medias = await ZnMedia.query().whereIn('id', imageIds).paginate(page, limit)

      return response.ok(medias)
    } catch (error) {
      return response.badRequest(error.message)
    }
  }

  /**
   * @syncInstagram
   * @tag Store
   */
  async syncInstagram({ request, response }: HttpContext) {
    const payload = await request.validateUsing(syncInstagramValidator)
    const store = await Store.query().where('id', payload.storeId).first()
    if (!store) {
      return response.notFound({ message: 'Store not found' })
    }
    await queue.dispatch(SyncInstagramMediaToStoreJob, { ...payload, store })

    return response.ok('Sync successfully')
  }

  /**
   * @verifyStore
   * @tag Store
   * @summary Verify store
   * @description Verify store descriptively
   * @requestBody {"storeId": "", "otp": "123456"}
   * @responseBody 200 - Verify successfully - OK
   * @responseBody 400 - Code Incorrect|Code is expired - Bad Request
   */
  async verifyStore({ request, response, auth }: HttpContext) {
    //Get store id and OTP
    const { storeId, otp } = request.all()
    const user = auth.getUserOrFail() as ZnUser

    //Match with database
    const requestClaim = await ZnClaimStore.query()
      .where('userId', user.id)
      .where('storeId', storeId)
      .preload('store')
      .preload('user')
      .orderBy('updatedAt', 'desc')
      .first()

    if (requestClaim) {
      if (otp && requestClaim.otp === otp) {
        requestClaim.otp = ''
        requestClaim.codeExpiredAt = null
        requestClaim.status = EClaimStore.APPROVE
        requestClaim.verifiedAt = DateTime.now()
        await requestClaim.save()

        await ZnStore.updateOrCreate(
          { id: requestClaim?.storeId },
          { verified: true, userId: requestClaim?.userId }
        )

        //Send notification
        await this.storeService.sendApprovalNotification(user, requestClaim.store)

        return response.ok('Verify successfully')
      } else {
        if (!requestClaim.codeExpiredAt) {
          return response.abort('Code is expired')
        }

        if (requestClaim.codeExpiredAt < DateTime.utc()) {
          return response.abort('Code is expired')
        }

        return response.abort('Code incorrect')
      }
    }
    //Response

    return response.notFound('Claim not found')
  }

  /**
   * @popular
   * @tag Store
   * @summary Get popular stores based on user preferences or random if not authenticated
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   */
  async popular({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10 } = request.qs()
      let requestUserId

      const authToken = request.header('Authorization') as string
      JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
        if (decodedToken) {
          requestUserId = decodedToken.userId
        }
      })

      const query = Store.query()
        .preload('logo')
        .preload('medias')
        .preload('posts', (postsQuery) => {
          postsQuery.where('isDraft', 0).whereNull('deletedAt').limit(3)
        })
        .preload('nailSystems')
        .withCount('posts', (postsCountQuery) => {
          postsCountQuery.where('isDraft', 0).whereNull('deletedAt')
        })
        .whereNull('deletedAt')

      if (requestUserId) {
        const favoriteResource = await ZnResourceFavorite.query()
          .where('userId', requestUserId)
          .first()

        if (favoriteResource) {
          let favoritePosts: string[] = []
          try {
            if (favoriteResource.posts) {
              if (Array.isArray(favoriteResource.posts)) {
                favoritePosts = favoriteResource.posts
              } else if (typeof favoriteResource.posts === 'string') {
                if (
                  favoriteResource.posts.trim().startsWith('[') &&
                  favoriteResource.posts.trim().endsWith(']')
                ) {
                  favoritePosts = JSON.parse(favoriteResource.posts)
                }
              }
            }
          } catch (parseError) {
            console.error('Error parsing favorite posts:', parseError)
          }

          if (favoritePosts.length > 0) {
            const postStores = await ZnPost.query()
              .select('storeId')
              .whereIn('id', favoritePosts)
              .whereNotNull('storeId')

            const storeIds = [...new Set(postStores.map((post) => post.storeId))]

            if (storeIds.length > 0) {
              query.whereIn('id', storeIds as string[]).orderByRaw('RAND()')
            } else {
              query.orderByRaw('RAND()')
            }
          } else {
            query.orderByRaw('RAND()')
          }
        } else {
          query.orderByRaw('RAND()')
        }
      } else {
        query.orderByRaw('RAND()')
      }

      const result = await query.paginate(page, limit)

      return response.ok(result.serialize())
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @enableManageBooking
   * @tag Store
   * @summary Enable manage booking for store
   * @description Enable manage booking for store
   * @requestBody {"storeId":"string"}
   * @responseBody 200 - Enable successfully - OK
   */
  async enableManageBooking({ request, response, auth }: HttpContext) {
    try {
      const { storeId } = request.all()
      const store = await Store.findOrFail(storeId)

      if (store.userId !== (auth.user as any)?.id) {
        return response.forbidden({ message: 'You are not authorized to manage this store' })
      }

      if (store.isManageBookingEnabled) {
        return response.badRequest({ message: 'Store booking management is already enabled' })
      }

      store.isManageBookingEnabled = true
      await store.save()
      return response.ok({ message: 'Enable successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @disableManageBooking
   * @tag Store
   * @summary Disable manage booking for store
   * @description Disable manage booking for store
   * @requestBody {"storeId":"string"}
   * @responseBody 200 - Disable successfully - OK
   */
  async disableManageBooking({ request, response, auth }: HttpContext) {
    try {
      const { storeId } = request.all()
      const store = await Store.findOrFail(storeId)

      if (store.userId !== (auth.user as any)?.id) {
        return response.forbidden({ message: 'You are not authorized to manage this store' })
      }

      if (!store.isManageBookingEnabled) {
        return response.badRequest({ message: 'Store booking management is already disabled' })
      }

      store.isManageBookingEnabled = false
      await store.save()
      return response.ok({ message: 'Disable successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @getTotalBookingsSummary
   * @tag Store
   * @summary Get total bookings count across all user's stores
   * @paramQuery startDate - Start date filter - @type(string)
   * @paramQuery endDate - End date filter - @type(string)
   * @responseBody 200 - {"totalBookings": number, "totalRevenue": number} - OK
   */
  async getTotalBookingsSummary({ request, response, auth }: HttpContext) {
    // @ts-ignore
    const user = auth.getUserOrFail().serialize() as ZnUser

    try {
      const { startDate, endDate } = request.qs()

      const query = db
        .from('zn_appointments')
        .join('zn_stores', 'zn_appointments.storeId', 'zn_stores.id')
        .where('zn_stores.userId', user.id)
        .whereIn('zn_appointments.status', [
          'booked',
          'confirmed',
          'checked-in',
          'in-service',
          'completed',
        ])
        .whereNull('zn_appointments.deletedAt')
        .count('zn_appointments.id as totalBookings')

      if (startDate) {
        query.where('zn_appointments.createdAt', '>=', DateTime.fromISO(startDate).toJSDate())
      }
      if (endDate) {
        query.where(
          'zn_appointments.createdAt',
          '<=',
          DateTime.fromISO(endDate).endOf('day').toJSDate()
        )
      }

      const result = await query.first()

      return response.ok({
        totalBookings: Number(result.totalBookings || 0),
      })
    } catch (error) {
      console.log('Error:', error)
      return response.internalServerError({
        message: error.message || 'Failed to fetch booking summary',
      })
    }
  }

  /**
   * @getStoresWithBookingCount
   * @tag Store
   * @summary Get user's stores with booking count for each store
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery startDate - Start date filter - @type(string)
   * @paramQuery endDate - End date filter - @type(string)
   * @responseBody 200 - <Store[]>.paginated() - List of stores with booking counts
   */
  async getStoresWithBookingCount({ request, response, auth }: HttpContext) {
    // @ts-ignore
    const user = auth.getUserOrFail().serialize() as ZnUser

    try {
      const { page = 1, limit = 10, startDate, endDate } = request.qs()

      let appointmentCountQuery = db
        .from('zn_appointments')
        .whereRaw('zn_appointments.storeId = zn_stores.id')
        .whereIn('status', ['booked', 'confirmed', 'checked-in', 'in-service', 'completed'])
        .whereNull('deletedAt')
        .count('* as count')

      if (startDate) {
        appointmentCountQuery.where('createdAt', '>=', DateTime.fromISO(startDate).toJSDate())
      }
      if (endDate) {
        appointmentCountQuery.where(
          'createdAt',
          '<=',
          DateTime.fromISO(endDate).endOf('day').toJSDate()
        )
      }

      const stores = await Store.query()
        .where('userId', user.id)
        .whereNull('deletedAt')
        .preload('logo')
        .preload('medias')
        .select(db.raw(`zn_stores.*, (${appointmentCountQuery.toQuery()}) as booked_count`))
        .orderBy('name', 'asc')
        .paginate(page, limit)

      const result = stores.serialize()
      result.data = result.data.map((store: any) => ({
        ...store,
        bookedCount: Number(store.booked_count || 0),
      }))

      return response.ok(result)
    } catch (error) {
      console.log('Error:', error)
      return response.internalServerError({
        message: error.message || 'Failed to fetch stores with booking count',
      })
    }
  }

  /**
   * @getStoreBookings
   * @tag Store
   * @summary Get all bookings for a specific store
   * @paramPath storeId - ID of Store - @type(string) @required
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery status - Booking status filter - @type(string)
   * @paramQuery startDate - Start date filter - @type(string)
   * @paramQuery endDate - End date filter - @type(string)
   * @responseBody 200 - <ZnAppointment[]>.paginated() - List of bookings
   * @responseBody 403 - {"message": "Unauthorized"} - Forbidden
   * @responseBody 404 - {"message": "Store not found"} - Not Found
   */
  async getStoreBookings({ request, response, auth, params }: HttpContext) {
    // @ts-ignore
    const user = auth.getUserOrFail().serialize() as ZnUser
    const storeId = params.storeId

    try {
      // Verify store ownership
      const store = await Store.query()
        .where('id', storeId)
        .where('userId', user.id)
        .whereNull('deletedAt')
        .first()

      if (!store) {
        return response.notFound({ message: 'Store not found or unauthorized' })
      }

      const { page = 1, limit = 10, status, startDate, endDate } = request.qs()

      const query = ZnAppointment.query()
        .where('storeId', storeId)
        .whereNull('deletedAt')
        .preload('customer', (customerQuery: any) => {
          customerQuery.select('id', 'firstName', 'lastName', 'email', 'phoneNumber', 'avatar')
        })
        .preload('services', (serviceQuery: any) => {
          serviceQuery.preload('image')
          serviceQuery.pivotColumns(['customPrice'])
        })
        .preload('packages', (packageQuery: any) => {
          packageQuery.preload('image')
          packageQuery.preload('services')
        })
        .orderBy('startTime', 'desc')

      if (status) {
        query.where('status', status)
      }

      if (startDate) {
        query.where('startTime', '>=', DateTime.fromISO(startDate).toJSDate())
      }

      if (endDate) {
        query.where('endTime', '<=', DateTime.fromISO(endDate).endOf('day').toJSDate())
      }

      const bookings = await query.paginate(page, limit)

      return response.ok(bookings.serialize())
    } catch (error) {
      console.log('Error:', error)
      return response.internalServerError({
        message: error.message || 'Failed to fetch store bookings',
      })
    }
  }
}
