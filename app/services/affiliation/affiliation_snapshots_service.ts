import { EAffiliateSnapshot<PERSON>ey } from "#constants/affiliate_snapshot_enum";
import { ECommissionGroupType } from "#constants/commission_group_type";
import ZnAffiliate from "#models/zn_affiliate";
import ZnAffiliateSnapshot from "#models/zn_affiliate_snapshot";
import { format } from "date-fns";
import AffiliationCommissionRateService from "./affiliation_commission_rate_service.js";
import { AffiliationCommissionService } from "./affiliation_commission_service.js";

export class AffiliationSnapshotsService {
  private commissionService: AffiliationCommissionService;
  private commissionRateService: AffiliationCommissionRateService;

  constructor() {
    this.commissionService = new AffiliationCommissionService();
    this.commissionRateService = new AffiliationCommissionRateService();
  }

  async getSnapshots(affiliateId: string, groupType: ECommissionGroupType, date?: Date): Promise<any> {
    const monthString = format(date ?? new Date(), 'yyyy-MM');
    const totalSales = await this.getSnapshotAsNumber(affiliateId, `${monthString}_${EAffiliateSnapshotKey.TOTAL_SALES}`);
    const commissionRate = await this.getSnapshotAsNumber(affiliateId, `${monthString}_${groupType}_${EAffiliateSnapshotKey.COMMISSION_RATE}`);
    const commissionRangeFrom = await this.getSnapshotAsNumber(affiliateId, `${monthString}_${groupType}_${EAffiliateSnapshotKey.COMMISSION_RANGE_FROM}`);
    const commissionRangeTo = await this.getSnapshotAsNumber(affiliateId, `${monthString}_${groupType}_${EAffiliateSnapshotKey.COMMISSION_RANGE_TO}`);
    const pendingCommissionAmount = await this.getSnapshotAsNumber(affiliateId, EAffiliateSnapshotKey.PENDING_COMMISSION_AMOUNT);
    const referredCustomersCount = await this.getSnapshotAsNumber(affiliateId, EAffiliateSnapshotKey.REFERRED_CUSTOMERS_COUNT);

    return {
      totalSales,
      commissionRate,
      commissionRangeFrom,
      commissionRangeTo,
      pendingCommissionAmount,
      referredCustomersCount
    }
  }

  async getSnapshotAsString(affiliateId: string, key: string): Promise<string> {
    const snapshot = await ZnAffiliateSnapshot.query()
      .where('affiliateId', affiliateId)
      .where('key', key)
      .first();

    return snapshot ? snapshot.value : '';
  }

  async getSnapshotAsNumber(affiliateId: string, key: string): Promise<number> {
    const snapshotValue = await this.getSnapshotAsString(affiliateId, key);
    if (snapshotValue.trim() !== "" && !Number.isNaN(snapshotValue)) {
      return parseFloat(snapshotValue);
    }
    return 0;
  }

  async updateTotalSalesSnapshot(affiliate: ZnAffiliate, startDate: Date, endDate: Date) {
    const totalSales = await this.commissionService.getTotalSales(affiliate.id, startDate, endDate);
    const monthString = format(startDate, 'yyyy-MM');
    await this.upsertSnapshot(affiliate, `${monthString}_${EAffiliateSnapshotKey.TOTAL_SALES}`, totalSales.toString());
    return totalSales;
  }

  async updateCommissionRateSnapshot(affiliate: ZnAffiliate, groupType: ECommissionGroupType, totalSales: number, startDate: Date) {
    const ranges = await this.commissionRateService.findCommissionRateRanges(affiliate.id, groupType, totalSales);
    const monthString = format(startDate, 'yyyy-MM');
    await Promise.all([
      this.upsertSnapshot(affiliate, `${monthString}_${groupType}_${EAffiliateSnapshotKey.COMMISSION_RATE}`, ranges.rate.toString()),
      this.upsertSnapshot(affiliate, `${monthString}_${groupType}_${EAffiliateSnapshotKey.COMMISSION_RANGE_FROM}`, ranges.from.toString()),
      this.upsertSnapshot(affiliate, `${monthString}_${groupType}_${EAffiliateSnapshotKey.COMMISSION_RANGE_TO}`, ranges.to.toString())
    ]);
  }

  async updatePendingCommissionsSnapshot(affiliate: ZnAffiliate) {
    const pendingCommissionAmount = await this.commissionService.getPendingCommissionAmount(affiliate.id);
    await this.upsertSnapshot(affiliate, EAffiliateSnapshotKey.PENDING_COMMISSION_AMOUNT, pendingCommissionAmount.toString());
    return pendingCommissionAmount;
  }

  async updateReferredCustomersCountSnapshot(affiliate: ZnAffiliate) {
    await affiliate.load('referredCustomers');
    await this.upsertSnapshot(affiliate, EAffiliateSnapshotKey.REFERRED_CUSTOMERS_COUNT, affiliate.referredCustomers.length.toString());
  }

  private async upsertSnapshot(affiliate: ZnAffiliate, key: string, value: string) {
    let snapshot = affiliate.snapshots.find((s) => s.key === key);
    if (snapshot) {
      snapshot.value = value;
      await snapshot.save();
    } else {
      snapshot = await ZnAffiliateSnapshot.create({
        key,
        value,
        affiliateId: affiliate.id
      });
    }
    return snapshot;
  }
}