import AdminChatbotController from "#adminControllers/chatbot/chat_bot_controller"
import router from "@adonisjs/core/services/router"
import { middleware } from "#start/kernel"

export default function adminChatbotRoutes() {
  router
    .group(() => {
      router.get('/',                 [AdminChatbotController, 'getChatRoom'])
      router.post('/resources',       [AdminChatbotController, 'listChatResources'])
      router.get('/:id/token',        [AdminChatbotController, 'createChatToken'])
      router.get('/:id/messages',     [AdminChatbotController, 'getChatMessages'])
      router.post('/:id/messages',    [AdminChatbotController, 'createChatMessage'])
      router.delete('/:id/messages',  [AdminChatbotController, 'clearChatMessages'])
      router.post('/:id/annotateMessages/:originalMessageId',  [AdminChatbotController, 'annotateMessage'])
      router.get('/rooms', [AdminChat<PERSON><PERSON>ontroller, 'getChatRooms'])
      router.post('/:id/takeover'           , [AdminChatbotController, 'takeOverRoom'])
      router.post  ('/:id/stopTakeover', [AdminChatbotController, 'stopTakingOver'])
      router.get('/rooms/:id', [AdminChatbotController, 'findChatRoom'])
    })
    .prefix('chat-bot')
    .middleware([ middleware.auth({ guards: ['jwt_admin'] as any }) ])
}
