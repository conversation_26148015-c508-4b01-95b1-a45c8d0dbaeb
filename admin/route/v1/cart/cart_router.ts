import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const AdminCartController = () => import('#adminControllers/cart/admin_cart_controller')

export default function adminCartRoutes() {
  router
    .group(() => {
      router.get('/', [AdminCartController, 'index'])
      router.get('/user/:userId', [AdminCartController, 'getUserCart'])
      router.get('/:id', [AdminCartController, 'show'])
      router.delete('/:id', [AdminCartController, 'destroy'])
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
    .prefix('carts')
}
