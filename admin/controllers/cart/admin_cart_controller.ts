import ZnCart from '#models/zn_cart'
import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { ACTION, RESOURCE } from '#constants/authorization'
import ZnCartSection from '#models/zn_cart_section'
import ZnCartItem from '#models/zn_cart_item'

export default class AdminCartController {
  /**
   * @index
   * @tag Admin Cart
   * @summary List all carts with pagination and filtering
   * @queryParam page - Page number - @type(number) @default(1)
   * @queryParam limit - Items per page - @type(number) @default(10)
   * @queryParam search - Search by user email/name - @type(string)
   * @queryParam status - Filter by cart status - @type(string)
   * @queryParam userId - Filter by specific user ID - @type(string)
   * @queryParam dateFrom - Filter carts created after date - @type(string)
   * @queryParam dateTo - Filter carts created before date - @type(string)
   * @responseBody 200 - {"data": [<ZnCart>], "meta": {"total": 100, "lastPage": 10}}
   * @responseBody 401 - Unauthorized access
   * @responseBody 400 - Bad request
   */
  async index({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10, search, status, userId, dateFrom, dateTo } = request.qs()

      const query = ZnCart.query()
        .preload('user')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery
            .preload('cartItems', (itemQuery) => {
              itemQuery.preload('product').preload('variant').preload('discount')
            })
            .preload('bundle')
        })
        .whereNull('deletedAt')

      if (search) {
        query.whereHas('user', (userQuery) => {
          userQuery
            .whereILike('email', `%${search}%`)
            .orWhereILike('firstName', `%${search}%`)
            .orWhereILike('lastName', `%${search}%`)
        })
      }

      if (status) {
        query.where('status', status)
      }

      if (userId) {
        query.where('userId', userId)
      }

      if (dateFrom) {
        query.where('createdAt', '>=', dateFrom)
      }

      if (dateTo) {
        query.where('createdAt', '<=', dateTo)
      }

      query.has('user')

      query.orderBy('updatedAt', 'desc')

      const result = await query.paginate(page, Math.min(limit, 100))

      const transformedData = result.all().map((cart) => {
        const cartData = cart.serialize()

        const itemCount = cart.cartSections.reduce((total: number, section: any) => {
          return total + parseInt(section.quantity || '0')
        }, 0)

        const totalValue = cart.cartSections.reduce(
          (total: number, section: any) => total + parseFloat(section.total || '0'),
          0
        )

        const lastActivity = cart.updatedAt

        return {
          ...cartData,
          itemCount,
          totalValue,
          lastActivity,
        }
      })

      return response.ok({
        data: transformedData,
        meta: result.toJSON().meta,
      })
    } catch (error) {
      logger.error('Failed to get carts', { error: error.message })
      return response.badRequest({
        success: false,
        message: 'Failed to get carts',
        error: error.message,
      })
    }
  }

  /**
   * @getUserCart
   * @tag Admin Cart
   * @summary Get cart for a specific user, creates new cart if none exists
   * @paramParam userId - User ID - @type(string)
   * @responseBody 200 - <ZnCart> with full details
   * @responseBody 400 - Bad request
   */
  async getUserCart({ params, response }: HttpContext) {
    try {
      const { userId } = params

      let cart = await ZnCart.query()
        .where('userId', userId)
        .whereNull('deletedAt')
        .preload('user')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery
            .whereNull('deletedAt')
            .preload('cartItems', (itemQuery) => {
              itemQuery
                .whereNull('deletedAt')
                .preload('product')
                .preload('variant')
                .preload('discount')
            })
            .preload('bundle')
        })
        .first()

      if (!cart) {
        cart = await ZnCart.create({ userId })
        await cart.load('user')
        await cart.load('cartSections')
      }

      const cartData = cart.serialize()

      const itemCount = cart.cartSections.reduce((total: number, section: any) => {
        return total + parseInt(section.quantity || '0')
      }, 0)

      const totalValue = cart.cartSections.reduce(
        (total: number, section: any) => total + parseFloat(section.total || '0'),
        0
      )

      return response.ok({
        ...cartData,
        itemCount,
        totalValue,
      })
    } catch (error) {
      logger.error('Failed to get user cart', { userId: params.userId, error: error.message })
      return response.badRequest({
        success: false,
        message: 'Failed to get user cart',
        error: error.message,
      })
    }
  }

  /**
   * @show
   * @tag Admin Cart
   * @summary Get specific cart by ID
   * @paramParam id - Cart ID - @type(string)
   * @responseBody 200 - <ZnCart> with full details
   * @responseBody 404 - Cart not found
   * @responseBody 400 - Bad request
   */
  async show({ params, response }: HttpContext) {
    try {
      const { id } = params

      const cart = await ZnCart.query()
        .where('id', id)
        .whereNull('deletedAt')
        .preload('user')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery
            .preload('cartItems', (itemQuery) => {
              itemQuery.preload('product').preload('variant').preload('discount')
            })
            .preload('bundle')
        })
        .first()

      if (!cart) {
        return response.notFound({
          success: false,
          message: 'Cart not found',
        })
      }

      const cartData = cart.serialize()

      const itemCount = cart.cartSections.reduce((total: number, section: any) => {
        return total + parseInt(section.quantity || '0')
      }, 0)

      const totalValue = cart.cartSections.reduce(
        (total: number, section: any) => total + parseFloat(section.total || '0'),
        0
      )

      return response.ok({
        ...cartData,
        itemCount,
        totalValue,
      })
    } catch (error) {
      logger.error('Failed to get cart', { id: params.id, error: error.message })
      return response.badRequest({
        success: false,
        message: 'Failed to get cart',
        error: error.message,
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Cart
   * @summary Delete a cart (hard delete) with all related sections and items
   * @paramParam id - Cart ID - @type(string)
   * @responseBody 204 - Cart successfully deleted
   * @responseBody 404 - Cart not found
   * @responseBody 400 - Bad request
   * @responseBody 403 - Forbidden - insufficient permissions
   */
  async destroy({ params, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.CART)

      const { id } = params
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const cart = await ZnCart.query()
        .where('id', id)
        .whereNull('deletedAt')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery.whereNull('deletedAt').preload('cartItems', (itemQuery) => {
            itemQuery.whereNull('deletedAt')
          })
        })
        .first()

      if (!cart) {
        return response.notFound({
          success: false,
          message: 'Cart not found or already deleted',
        })
      }

      const cartSectionIds = cart.cartSections.map((section) => section.id)

      if (cartSectionIds.length > 0) {
        await ZnCartItem.query().whereIn('cartSectionId', cartSectionIds).delete()
      }

      if (cartSectionIds.length > 0) {
        await ZnCartSection.query().whereIn('id', cartSectionIds).delete()
      }

      await cart.delete()

      return response.ok({ message: 'Cart deleted successfully' })
    } catch (error) {
      return response.badRequest({
        success: false,
        message: 'Failed to delete cart',
        error: error.message,
      })
    }
  }
}
