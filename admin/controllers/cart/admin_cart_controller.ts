import ZnCart from '#models/zn_cart'
import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { ACTION, RESOURCE } from '#constants/authorization'
import ZnCartSection from '#models/zn_cart_section'
import ZnCartItem from '#models/zn_cart_item'
import { AdminCartService } from '#adminServices/cart/admin_cart_service'
import {
  adminAddItemValidator,
  adminAddBundleValidator,
  adminUpdateQuantityValidator,
  adminUpdateCartItemValidator,
  adminDeleteCartSectionValidator,
} from '#adminValidators/cart/cart_validator'

export default class AdminCartController {
  private adminCartService: AdminCartService

  constructor() {
    this.adminCartService = new AdminCartService()
  }
  /**
   * @index
   * @tag Admin Cart
   * @summary List all carts with pagination and filtering
   * @queryParam page - Page number - @type(number) @default(1)
   * @queryParam limit - Items per page - @type(number) @default(10)
   * @queryParam search - Search by user email/name - @type(string)
   * @queryParam status - Filter by cart status - @type(string)
   * @queryParam userId - Filter by specific user ID - @type(string)
   * @queryParam dateFrom - Filter carts created after date - @type(string)
   * @queryParam dateTo - Filter carts created before date - @type(string)
   * @responseBody 200 - {"data": [<ZnCart>], "meta": {"total": 100, "lastPage": 10}}
   * @responseBody 401 - Unauthorized access
   * @responseBody 400 - Bad request
   */
  async index({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10, search, status, userId, dateFrom, dateTo } = request.qs()

      const query = ZnCart.query()
        .preload('user')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery
            .preload('cartItems', (itemQuery) => {
              itemQuery.preload('product').preload('variant').preload('discount')
            })
            .preload('bundle')
        })
        .whereNull('deletedAt')

      if (search) {
        query.whereHas('user', (userQuery) => {
          userQuery
            .whereILike('email', `%${search}%`)
            .orWhereILike('firstName', `%${search}%`)
            .orWhereILike('lastName', `%${search}%`)
        })
      }

      if (status) {
        query.where('status', status)
      }

      if (userId) {
        query.where('userId', userId)
      }

      if (dateFrom) {
        query.where('createdAt', '>=', dateFrom)
      }

      if (dateTo) {
        query.where('createdAt', '<=', dateTo)
      }

      query.has('user')

      query.orderBy('updatedAt', 'desc')

      const result = await query.paginate(page, Math.min(limit, 100))

      const transformedData = result.all().map((cart) => {
        const cartData = cart.serialize()

        const itemCount = cart.cartSections.reduce((total: number, section: any) => {
          return total + parseInt(section.quantity || '0')
        }, 0)

        const totalValue = cart.cartSections.reduce(
          (total: number, section: any) => total + parseFloat(section.total || '0'),
          0
        )

        const lastActivity = cart.updatedAt

        return {
          ...cartData,
          itemCount,
          totalValue,
          lastActivity,
        }
      })

      return response.ok({
        data: transformedData,
        meta: result.toJSON().meta,
      })
    } catch (error) {
      logger.error('Failed to get carts', { error: error.message })
      return response.badRequest({
        success: false,
        message: 'Failed to get carts',
        error: error.message,
      })
    }
  }

  /**
   * @getUserCart
   * @tag Admin Cart
   * @summary Get cart for a specific user, creates new cart if none exists
   * @paramParam userId - User ID - @type(string)
   * @responseBody 200 - <ZnCart> with full details
   * @responseBody 400 - Bad request
   */
  async getUserCart({ params, response }: HttpContext) {
    try {
      const { userId } = params

      let cart = await ZnCart.query()
        .where('userId', userId)
        .whereNull('deletedAt')
        .preload('user')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery
            .whereNull('deletedAt')
            .preload('cartItems', (itemQuery) => {
              itemQuery
                .whereNull('deletedAt')
                .preload('product')
                .preload('variant')
                .preload('discount')
            })
            .preload('bundle')
        })
        .first()

      if (!cart) {
        cart = await ZnCart.create({ userId })
        await cart.load('user')
        await cart.load('cartSections')
      }

      const cartData = cart.serialize()

      const itemCount = cart.cartSections.reduce((total: number, section: any) => {
        return total + parseInt(section.quantity || '0')
      }, 0)

      const totalValue = cart.cartSections.reduce(
        (total: number, section: any) => total + parseFloat(section.total || '0'),
        0
      )

      return response.ok({
        ...cartData,
        itemCount,
        totalValue,
      })
    } catch (error) {
      logger.error('Failed to get user cart', { userId: params.userId, error: error.message })
      return response.badRequest({
        success: false,
        message: 'Failed to get user cart',
        error: error.message,
      })
    }
  }

  /**
   * @show
   * @tag Admin Cart
   * @summary Get specific cart by ID
   * @paramParam id - Cart ID - @type(string)
   * @responseBody 200 - <ZnCart> with full details
   * @responseBody 404 - Cart not found
   * @responseBody 400 - Bad request
   */
  async show({ params, response }: HttpContext) {
    try {
      const { id } = params

      const cart = await ZnCart.query()
        .where('id', id)
        .whereNull('deletedAt')
        .preload('user')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery
            .preload('cartItems', (itemQuery) => {
              itemQuery.preload('product').preload('variant').preload('discount')
            })
            .preload('bundle')
        })
        .first()

      if (!cart) {
        return response.notFound({
          success: false,
          message: 'Cart not found',
        })
      }

      const cartData = cart.serialize()

      const itemCount = cart.cartSections.reduce((total: number, section: any) => {
        return total + parseInt(section.quantity || '0')
      }, 0)

      const totalValue = cart.cartSections.reduce(
        (total: number, section: any) => total + parseFloat(section.total || '0'),
        0
      )

      return response.ok({
        ...cartData,
        itemCount,
        totalValue,
      })
    } catch (error) {
      logger.error('Failed to get cart', { id: params.id, error: error.message })
      return response.badRequest({
        success: false,
        message: 'Failed to get cart',
        error: error.message,
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Cart
   * @summary Delete a cart (hard delete) with all related sections and items
   * @paramParam id - Cart ID - @type(string)
   * @responseBody 204 - Cart successfully deleted
   * @responseBody 404 - Cart not found
   * @responseBody 400 - Bad request
   * @responseBody 403 - Forbidden - insufficient permissions
   */
  async destroy({ params, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.CART)

      const { id } = params
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const cart = await ZnCart.query()
        .where('id', id)
        .whereNull('deletedAt')
        .preload('cartSections', (sectionQuery) => {
          sectionQuery.whereNull('deletedAt').preload('cartItems', (itemQuery) => {
            itemQuery.whereNull('deletedAt')
          })
        })
        .first()

      if (!cart) {
        return response.notFound({
          success: false,
          message: 'Cart not found or already deleted',
        })
      }

      const cartSectionIds = cart.cartSections.map((section) => section.id)

      if (cartSectionIds.length > 0) {
        await ZnCartItem.query().whereIn('cartSectionId', cartSectionIds).delete()
      }

      if (cartSectionIds.length > 0) {
        await ZnCartSection.query().whereIn('id', cartSectionIds).delete()
      }

      await cart.delete()

      return response.ok({ message: 'Cart deleted successfully' })
    } catch (error) {
      return response.badRequest({
        success: false,
        message: 'Failed to delete cart',
        error: error.message,
      })
    }
  }

  /**
   * @addItemToCart
   * @tag Admin Cart
   * @summary Add product item to cart - giống logic app API 100%
   * @paramParam cartId - Cart ID - @type(string)
   * @requestBody <adminAddItemValidator>
   * @responseBody 201 - <ZnCartSection> with cart items
   * @responseBody 400 - Bad request
   * @responseBody 403 - Forbidden - insufficient permissions
   */
  async addItemToCart({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.CART)

      const { cartId } = params
      const payload = await request.validateUsing(adminAddItemValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const result = await this.adminCartService.addItemToCart({
        cartId,
        ...payload,
      })

      await result.load('cartItems', (query) => {
        query.preload('discount').preload('variant').preload('product')
      })
      await result.load('bundle')

      return response.created({
        success: true,
        data: result,
        message: 'Item added to cart successfully',
      })
    } catch (error) {
      logger.error('Failed to add item to cart', {
        cartId: params.cartId,
        error: error.message,
        adminId: (auth.user as any)?.id,
      })
      return response.badRequest({
        success: false,
        message: 'Failed to add item to cart',
        error: error.message,
      })
    }
  }

  /**
   * @addBundleToCart
   * @tag Admin Cart
   * @summary Add bundle to cart - giống logic app API 100%
   * @paramParam cartId - Cart ID - @type(string)
   * @requestBody <adminAddBundleValidator>
   * @responseBody 201 - <ZnCartSection> with bundle items
   * @responseBody 400 - Bad request
   * @responseBody 403 - Forbidden - insufficient permissions
   */
  async addBundleToCart({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.CART)

      const { cartId } = params
      const payload = await request.validateUsing(adminAddBundleValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const result = await this.adminCartService.addBundleToCart({
        cartId,
        ...payload,
      })

      await result.load('cartItems', (query) => {
        query.preload('discount').preload('variant').preload('product')
      })
      await result.load('bundle')

      return response.created({
        success: true,
        data: result,
        message: 'Bundle added to cart successfully',
      })
    } catch (error) {
      logger.error('Failed to add bundle to cart', {
        cartId: params.cartId,
        error: error.message,
        adminId: (auth.user as any)?.id,
      })
      return response.badRequest({
        success: false,
        message: 'Failed to add bundle to cart',
        error: error.message,
      })
    }
  }

  /**
   * @updateSectionQuantity
   * @tag Admin Cart
   * @summary Update cart section quantity - giống logic app API 100%
   * @paramParam cartSectionId - Cart Section ID - @type(string)
   * @requestBody <adminUpdateQuantityValidator>
   * @responseBody 200 - <ZnCartSection> updated section or null if deleted
   * @responseBody 400 - Bad request
   * @responseBody 403 - Forbidden - insufficient permissions
   * @responseBody 404 - Cart section not found
   */
  async updateSectionQuantity({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CART)

      const { cartSectionId } = params
      const payload = await request.validateUsing(adminUpdateQuantityValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const result = await this.adminCartService.updateSectionQuantity({
        cartSectionId,
        ...payload,
      })

      if (result) {
        await result.load('cartItems', (query) => {
          query.preload('discount').preload('variant').preload('product')
        })
        await result.load('bundle')
      }

      return response.ok({
        success: true,
        data: result,
        message: result ? 'Section quantity updated successfully' : 'Section removed successfully',
      })
    } catch (error) {
      logger.error('Failed to update section quantity', {
        cartSectionId: params.cartSectionId,
        error: error.message,
        adminId: (auth.user as any)?.id,
      })
      return response.badRequest({
        success: false,
        message: 'Failed to update section quantity',
        error: error.message,
      })
    }
  }

  /**
   * @updateCartItem
   * @tag Admin Cart
   * @summary Update individual cart item properties
   * @paramParam cartItemId - Cart Item ID - @type(string)
   * @requestBody <adminUpdateCartItemValidator>
   * @responseBody 200 - <ZnCartItem> updated item
   * @responseBody 400 - Bad request
   * @responseBody 403 - Forbidden - insufficient permissions
   * @responseBody 404 - Cart item not found
   */
  async updateCartItem({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CART)

      const { cartItemId } = params
      const payload = await request.validateUsing(adminUpdateCartItemValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      const result = await this.adminCartService.updateCartItem({
        cartItemId,
        ...payload,
      })

      await result.load('product')
      await result.load('variant')
      await result.load('discount')

      return response.ok({
        success: true,
        data: result,
        message: 'Cart item updated successfully',
      })
    } catch (error) {
      logger.error('Failed to update cart item', {
        cartItemId: params.cartItemId,
        error: error.message,
        adminId: (auth.user as any)?.id,
      })
      return response.badRequest({
        success: false,
        message: 'Failed to update cart item',
        error: error.message,
      })
    }
  }

  /**
   * @deleteCartSection
   * @tag Admin Cart
   * @summary Delete cart section - giống logic app API 100%
   * @paramParam cartSectionId - Cart Section ID - @type(string)
   * @requestBody <adminDeleteCartSectionValidator>
   * @responseBody 200 - Success message
   * @responseBody 400 - Bad request
   * @responseBody 403 - Forbidden - insufficient permissions
   * @responseBody 404 - Cart section not found
   */
  async deleteCartSection({ params, request, response, auth, bouncer }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.CART)

      const { cartSectionId } = params
      const payload = await request.validateUsing(adminDeleteCartSectionValidator)
      const adminId = (auth.user as any)?.id

      if (!adminId) {
        return response.unauthorized({
          success: false,
          message: 'Authentication required',
        })
      }

      await this.adminCartService.deleteCartSection(cartSectionId, payload.notes)

      return response.ok({
        success: true,
        message: 'Cart section deleted successfully',
      })
    } catch (error) {
      logger.error('Failed to delete cart section', {
        cartSectionId: params.cartSectionId,
        error: error.message,
        adminId: (auth.user as any)?.id,
      })
      return response.badRequest({
        success: false,
        message: 'Failed to delete cart section',
        error: error.message,
      })
    }
  }
}
