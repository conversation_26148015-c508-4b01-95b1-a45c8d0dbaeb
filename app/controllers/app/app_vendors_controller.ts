import ZnProduct from "#models/zn_product";
import ZnProductVariant from "#models/zn_product_variant";
import ZnVendor from "#models/zn_vendor";
import { HttpContext } from "@adonisjs/core/http";

export default class AppVendorsController {
  /**
   * @show
   * @tag App Vendors
   * @summary Get vendor public details
   * @paramPath id - ID of Vendor - @type(string) @required
   * @responseBody 200 - <ZnVendor> - Vendor details
   * @responseBody 404 - Vendor Not Found - Not Found
   * @responseBody 500 - {"message":"Something went wrong!","error":{}} - Internal Server Error
   */
  public async show({ params, response }: HttpContext) {
    try {
      const vendorId = params.id

      const vendor = await ZnVendor.query()
        .where({ id: vendorId })
        .first()

      if (!vendor) { return response.notFound("Vendor not found") }

      return response.ok(vendor)

    } catch (error) {
      console.error('Error fetching vendor:', error)
      return response.internalServerError({
        message: 'Something went wrong!',
        error,
      })
    }
  }

  /**
   * @listProducts
   * @tag App Vendors
   * @summary Fetch vendor's products
   * @paramPath id - ID of Vendor - @type(string) @required
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery pageSize - Page Size (default 10) - @type(number)
   * @paramQuery search - Search Term - @type(string)
   * @paramQuery sortBy - Sort By - @enum(a2z,z2a,lowest,highest,oldest,newest,best)
   * @responseBody 200 - <ZnProduct[]>.paginated() - Vendor's top selling product variants
   * @responseBody 500 - {"message":"Something went wrong!","error":{}} - Internal Server Error
   */
  public async listProducts({ params, request, response }: HttpContext) {
    try {
      const vendorId = params.id

      const {
        page = 1,
        pageSize = 10,
        search,
        sortBy,
      } = request.qs()

      const query = ZnProduct.query()
        .where({
          vendorId,
          isGift: false,
          status: 'active'
        })
        .has('variant')
        .preload('variant')
        .preload('reviewsSummary')
        .preload('image')

      if (search) {
        const searchTitle = search.replaceAll(' ', '%')
        const productIds = (
          await ZnProductVariant.query()
            .whereILike('title', `%${searchTitle}%`)
            .orWhereILike('barcode', `%${search}%`)
            .orWhereILike('sku', `%${search}%`)
            .select('productId')
            .groupBy('productId')
            .limit(50)
        ).map((v) => v.productId)

        query.where((queryBuilder) => {
          queryBuilder
            .whereIn('id', productIds)
            .orWhereILike('title', `%${searchTitle}%`)
        })
      }

      if (sortBy) {
        switch (sortBy) {
          case 'a2z': {
            query.orderBy('title', 'asc')
            break
          }
          case 'z2a': {
            query.orderBy('title', 'desc')
            break
          }
          case 'lowest': {
            query.orderBy('price', 'asc')
            break
          }
          case 'highest': {
            query.orderBy('price', 'desc')
            break
          }
          case 'oldest': {
            query.orderBy('createdAt', 'asc')
            break
          }
          case 'newest': {
            query.orderBy('createdAt', 'desc')
            break
          }
          case 'best':
          default: { }
        }
      }

      const result = await query.paginate(page, pageSize)

      return response.ok(result)

    } catch (error) {
      console.error('Error fetching vendor products:', error)
      return response.internalServerError({
        message: 'Something went wrong!',
        error,
      })
    }
  }
}
