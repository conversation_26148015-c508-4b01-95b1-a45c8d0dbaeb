# Admin Cart Management - Complete Integration Guide

## 📋 Overview

The Admin Cart Management API provides comprehensive cart manipulation capabilities for administrators, allowing them to manage user shopping carts with full audit trail tracking. This API mirrors 100% of the app-side cart logic while adding administrative controls and audit capabilities.

**Key Features:**

- ✅ **100% Logic Consistency** with app APIs
- ✅ **Complete Cart Management** (add, edit, delete items/sections)
- ✅ **Admin Audit Trail** with detailed logging
- ✅ **Bundle & Product Support** with discount calculations
- ✅ **Role-based Access Control** with permission checks
- ✅ **Comprehensive Error Handling** with detailed responses

## 🔗 Base URL

```
https://your-domain.com/v1/admin/carts
```

## 🔐 Authentication

All endpoints require JWT authentication with admin privileges:

```http
Authorization: Bearer <admin_jwt_token>
```

## 📊 API Endpoints Overview

| Method   | Endpoint                                           | Purpose                       | Permission Required |
| -------- | -------------------------------------------------- | ----------------------------- | ------------------- |
| `GET`    | `/v1/admin/carts`                                  | List all carts with filtering | `READ` on `CART`    |
| `GET`    | `/v1/admin/carts/user/:userId`                     | Get or create user cart       | `READ` on `CART`    |
| `GET`    | `/v1/admin/carts/:id`                              | Get specific cart details     | `READ` on `CART`    |
| `DELETE` | `/v1/admin/carts/:id`                              | Delete entire cart            | `DELETE` on `CART`  |
| `POST`   | `/v1/admin/carts/:cartId/items`                    | Add product to cart           | `CREATE` on `CART`  |
| `POST`   | `/v1/admin/carts/:cartId/bundles`                  | Add bundle to cart            | `CREATE` on `CART`  |
| `PUT`    | `/v1/admin/carts/sections/:cartSectionId/quantity` | Update section quantity       | `UPDATE` on `CART`  |
| `PUT`    | `/v1/admin/carts/items/:cartItemId`                | Modify individual item        | `UPDATE` on `CART`  |
| `DELETE` | `/v1/admin/carts/sections/:cartSectionId`          | Remove cart section           | `DELETE` on `CART`  |

---

## 🚀 Quick Start Integration

### Step 1: Setup Authentication

```javascript
const adminToken = 'your-admin-jwt-token'
const baseURL = 'https://your-domain.com/v1/admin/carts'

const headers = {
  'Authorization': `Bearer ${adminToken}`,
  'Content-Type': 'application/json',
}
```

### Step 2: Get User Cart

```javascript
// Get or create cart for user
const getUserCart = async (userId) => {
  const response = await fetch(`${baseURL}/user/${userId}`, {
    method: 'GET',
    headers,
  })
  return response.json()
}
```

### Step 3: Add Items to Cart

```javascript
// Add product item
const addItem = async (cartId, variantId, quantity, notes = '') => {
  const response = await fetch(`${baseURL}/${cartId}/items`, {
    method: 'POST',
    headers,
    body: JSON.stringify({
      variantId,
      quantity,
      notes,
    }),
  })
  return response.json()
}

// Add bundle
const addBundle = async (cartId, bundleId, quantity, notes = '') => {
  const response = await fetch(`${baseURL}/${cartId}/bundles`, {
    method: 'POST',
    headers,
    body: JSON.stringify({
      bundleId,
      quantity,
      notes,
    }),
  })
  return response.json()
}
```

---

## 📖 Detailed API Documentation

### 1. List All Carts

**Endpoint:** `GET /v1/admin/carts`

**Query Parameters:**

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `search` (optional): Search by user email/name
- `status` (optional): Filter by cart status
- `userId` (optional): Filter by specific user ID
- `dateFrom` (optional): Filter carts created after date
- `dateTo` (optional): Filter carts created before date

**Example Request:**

```bash
GET /v1/admin/carts?page=1&limit=20&search=john&status=active
```

**Response:**

```json
{
  "data": [
    {
      "id": "cart-uuid-1",
      "userId": "user-uuid-1",
      "user": {
        "id": "user-uuid-1",
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe"
      },
      "cartSections": [...],
      "itemCount": 5,
      "totalValue": 299.99,
      "lastActivity": "2024-01-15T14:20:00Z"
    }
  ],
  "meta": {
    "total": 150,
    "perPage": 20,
    "currentPage": 1,
    "lastPage": 8
  }
}
```

### 2. Get or Create User Cart

**Endpoint:** `GET /v1/admin/carts/user/:userId`

**Purpose:** Retrieve existing cart for a user or create a new one if none exists

**Example Request:**

```bash
GET /v1/admin/carts/user/user-uuid-123
```

**Response:**

```json
{
  "id": "cart-uuid-456",
  "userId": "user-uuid-123",
  "user": {
    "id": "user-uuid-123",
    "email": "<EMAIL>",
    "firstName": "Jane",
    "lastName": "Smith"
  },
  "cartSections": [
    {
      "id": "section-uuid-789",
      "section": "product",
      "title": "Product: Premium Widget",
      "quantity": 2,
      "total": 199.98,
      "price": 99.99,
      "cartItems": [...]
    }
  ],
  "itemCount": 2,
  "totalValue": 199.98
}
```

### 3. Add Product to Cart

**Endpoint:** `POST /v1/admin/carts/:cartId/items`

**Purpose:** Add a product variant to cart - **logic giống app API 100%**

**Request Body:**

```json
{
  "variantId": "variant-uuid-123",
  "quantity": 3,
  "affiliateId": "affiliate-uuid-456", // Optional
  "notes": "Added by admin for customer request" // Optional
}
```

**Validation Rules:**

- `variantId`: Required, must be valid product variant ID
- `quantity`: Required, must be positive integer
- `affiliateId`: Optional, must be valid affiliate ID if provided
- `notes`: Optional, admin modification notes

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "section-uuid-789",
    "cartId": "cart-uuid-456",
    "quantity": 3,
    "total": 299.97,
    "rawTotal": 299.97,
    "price": 99.99,
    "rawPrice": 99.99,
    "cartItems": [
      {
        "id": "item-uuid-101",
        "productName": "Premium Widget",
        "variantName": "Large - Blue",
        "quantity": 3,
        "price": 99.99
      }
    ]
  },
  "message": "Item added to cart successfully"
}
```

### 4. Add Bundle to Cart

**Endpoint:** `POST /v1/admin/carts/:cartId/bundles`

**Purpose:** Add a bundle product to cart - **logic giống app API 100%**

**Request Body:**

```json
{
  "bundleId": "bundle-uuid-789",
  "quantity": 1,
  "discountId": "discount-uuid-123", // Optional
  "affiliateId": "affiliate-uuid-456", // Optional
  "notes": "Added bundle promotion", // Optional
  "items": [
    // Optional - for product bundles
    {
      "variantId": "variant-uuid-1",
      "itemId": "item-1"
    }
  ],
  "collections": [
    // Optional - for collection bundles
    {
      "id": "collection-uuid-1",
      "items": [
        {
          "id": "item-1",
          "variants": [
            {
              "id": "variant-uuid-1",
              "quantity": 1
            }
          ]
        }
      ]
    }
  ]
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "section-uuid-101",
    "cartId": "cart-uuid-456",
    "bundleId": "bundle-uuid-789",
    "quantity": 1,
    "total": 149.99,
    "title": "Bundle: Holiday Special",
    "cartItems": [
      {
        "id": "item-uuid-201",
        "productName": "Widget A",
        "variantName": "Standard",
        "quantity": 1,
        "price": 79.99,
        "bundleName": "Holiday Special"
      },
      {
        "id": "item-uuid-202",
        "productName": "Widget B",
        "variantName": "Premium",
        "quantity": 1,
        "price": 69.99,
        "bundleName": "Holiday Special"
      }
    ]
  },
  "message": "Bundle added to cart successfully"
}
```

### 5. Update Section Quantity

**Endpoint:** `PUT /v1/admin/carts/sections/:cartSectionId/quantity`

**Purpose:** Update the quantity of a cart section - **logic giống app API 100%**

**Request Body:**

```json
{
  "quantity": 5, // Set to 0 to remove section
  "notes": "Updated quantity based on customer request" // Optional
}
```

**Validation Rules:**

- `quantity`: Required, must be non-negative integer (0 removes the section)
- `notes`: Optional, admin modification notes

**Response (Quantity > 0):**

```json
{
  "success": true,
  "data": {
    "id": "section-uuid-789",
    "cartId": "cart-uuid-456",
    "quantity": 5,
    "total": 499.95,
    "price": 99.99,
    "cartItems": [...]
  },
  "message": "Section quantity updated successfully"
}
```

**Response (Quantity = 0):**

```json
{
  "success": true,
  "data": null,
  "message": "Section removed successfully"
}
```

### 6. Update Individual Cart Item

**Endpoint:** `PUT /v1/admin/carts/items/:cartItemId`

**Purpose:** Modify individual cart item properties (quantity, price)

**Request Body:**

```json
{
  "quantity": 3, // Optional
  "price": 89.99, // Optional - admin can override price
  "notes": "Applied special pricing for customer" // Optional
}
```

**Validation Rules:**

- `quantity`: Optional, must be positive integer if provided
- `price`: Optional, must be positive number if provided
- `notes`: Optional, admin modification notes

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "item-uuid-101",
    "cartSectionId": "section-uuid-789",
    "productId": "product-uuid-123",
    "variantId": "variant-uuid-456",
    "quantity": 3,
    "price": 89.99,
    "productName": "Premium Widget",
    "variantName": "Large - Blue",
    "product": {...},
    "variant": {...}
  },
  "message": "Cart item updated successfully"
}
```

### 7. Delete Cart Section

**Endpoint:** `DELETE /v1/admin/carts/sections/:cartSectionId`

**Purpose:** Remove an entire cart section and all its items - **logic giống app API 100%**

**Request Body:**

```json
{
  "notes": "Removed at customer request" // Optional
}
```

**Response:**

```json
{
  "success": true,
  "message": "Cart section deleted successfully"
}
```

### 8. Delete Entire Cart

**Endpoint:** `DELETE /v1/admin/carts/:id`

**Purpose:** Permanently delete a cart and all its contents (hard delete)

**Response:**

```json
{
  "message": "Cart deleted successfully"
}
```

---

## 🔄 Logic Consistency với App APIs

### 1. Item Merging Behavior

- **Duplicate Items**: Khi thêm item đã tồn tại, quantity sẽ được cộng dồn
- **Same Logic**: Sử dụng `ShopCartService.addItemToCart()` để đảm bảo logic merge giống 100%
- **Variant Checking**: Kiểm tra `availableForSale` và product status như app API

### 2. Bundle Handling

- **Bundle Merging**: Bundle với cùng ID sẽ được merge quantity như app API
- **Discount Calculation**: Sử dụng `CheckoutService` để tính discount giống app API
- **Bundle Types**: Hỗ trợ cả product bundles và collection bundles
- **BOGO Logic**: Buy-one-get-one logic được áp dụng như app API

### 3. Price Calculation

- **ShopCartService**: Sử dụng `ShopCartService` để đảm bảo logic pricing giống 100%
- **Raw Price**: Lưu trữ raw price và final price như app API
- **Bundle Discounts**: Discount application logic giống app API
- **Affiliate Pricing**: Hỗ trợ affiliate pricing như app API

### 4. Section Management

- **Section Creation**: Logic tạo section giống app API
- **Quantity Updates**: Cập nhật quantity và recalculate totals như app API
- **Section Deletion**: Quantity = 0 sẽ xóa section như app API
- **Bundle Detection**: Tự động detect và apply bundle như app API

---

## 🔧 Integration Workflow

### Typical Cart Management Workflow

```mermaid
graph TD
    A[Get User Cart] --> B{Cart Exists?}
    B -->|No| C[Create New Cart]
    B -->|Yes| D[Load Existing Cart]
    C --> E[Add Items/Bundles]
    D --> E
    E --> F[Update Quantities]
    F --> G[Modify Individual Items]
    G --> H[Remove Unwanted Items]
    H --> I[Final Review]
    I --> J[Complete]
```

### Step-by-Step Integration

1. **Initialize Cart Management**

```javascript
const cartManager = {
  baseURL: 'https://your-domain.com/v1/admin/carts',
  headers: {
    'Authorization': `Bearer ${adminToken}`,
    'Content-Type': 'application/json',
  },
}
```

2. **Get or Create User Cart**

```javascript
const cart = await cartManager.getUserCart(userId)
```

3. **Add Products and Bundles**

```javascript
// Add individual products
await cartManager.addItem(cart.id, variantId, quantity, notes)

// Add bundles
await cartManager.addBundle(cart.id, bundleId, quantity, notes)
```

4. **Modify Cart Contents**

```javascript
// Update section quantities
await cartManager.updateSectionQuantity(sectionId, newQuantity, notes)

// Update individual items
await cartManager.updateCartItem(itemId, { quantity: 3, price: 89.99 }, notes)
```

5. **Remove Items**

```javascript
// Remove entire sections
await cartManager.deleteSection(sectionId, notes)

// Or set quantity to 0
await cartManager.updateSectionQuantity(sectionId, 0, notes)
```

### When to Use Each API

| **Use Case**        | **Primary API**                             | **Alternative**                                    | **Notes**                        |
| ------------------- | ------------------------------------------- | -------------------------------------------------- | -------------------------------- |
| **View User Cart**  | `GET /v1/admin/carts/user/:userId`          | `GET /v1/admin/carts/:id`                          | Creates cart if none exists      |
| **Add New Product** | `POST /v1/admin/carts/:cartId/items`        | -                                                  | Handles duplicates automatically |
| **Add Bundle**      | `POST /v1/admin/carts/:cartId/bundles`      | -                                                  | Creates multiple cart items      |
| **Change Quantity** | `PUT /v1/admin/carts/sections/:id/quantity` | -                                                  | Updates entire section           |
| **Modify Price**    | `PUT /v1/admin/carts/items/:id`             | -                                                  | Individual item modification     |
| **Remove Section**  | `DELETE /v1/admin/carts/sections/:id`       | `PUT /v1/admin/carts/sections/:id/quantity` with 0 | Complete removal                 |
| **Delete Cart**     | `DELETE /v1/admin/carts/:id`                | -                                                  | Permanent deletion               |

---

## ⚠️ Error Handling

### Common Error Responses

**400 Bad Request:**

```json
{
  "success": false,
  "message": "Failed to add item to cart",
  "error": "Variant not available for sale"
}
```

**401 Unauthorized:**

```json
{
  "success": false,
  "message": "Authentication required"
}
```

**403 Forbidden:**

```json
{
  "success": false,
  "message": "Insufficient permissions"
}
```

**404 Not Found:**

```json
{
  "success": false,
  "message": "Cart section not found"
}
```

**422 Validation Error:**

```json
{
  "errors": [
    {
      "field": "variantId",
      "message": "The variantId field must be a valid UUID"
    }
  ]
}
```

### Error Scenarios and Solutions

| **Error**                        | **Cause**                    | **Solution**                                  |
| -------------------------------- | ---------------------------- | --------------------------------------------- |
| `Cart not found`                 | Invalid cart ID              | Verify cart exists with `GET /v1/admin/carts` |
| `Product variant not found`      | Invalid variant ID           | Check product catalog for valid variant IDs   |
| `Variant not available for sale` | Product out of stock         | Check inventory status                        |
| `Bundle product not found`       | Invalid bundle ID            | Verify bundle exists and is active            |
| `Insufficient permissions`       | Admin lacks CART permissions | Ensure admin has proper role assignments      |

---

## 📊 Admin Audit Trail

### Automatic Tracking

Every cart modification is automatically tracked with:

- **Admin ID**: Who made the change
- **Action Type**: What was done (add, update, remove, modify)
- **Timestamp**: When the change occurred
- **Notes**: Optional explanation for the change
- **Target Resource**: What was modified (cart, section, item)

### Audit Fields in Responses

```json
{
  "lastModifiedByAdminId": "admin-uuid-1",
  "lastAdminAction": "add",
  "lastAdminModifiedAt": "2024-01-15T14:20:00Z",
  "lastAdminModificationNotes": "Customer requested additional items"
}
```

### Audit History Retrieval

Use `GET /v1/admin/carts/:id` to retrieve full audit history for any cart.

---

## 🔒 Security & Permissions

### Authentication Requirements

- **JWT Admin Token**: Required for all endpoints
- **Token Validation**: Automatic validation of admin privileges
- **Session Management**: Proper token expiration handling

### Authorization Checks

- **Resource-based Permissions**: `CART` resource permissions required
- **Action-based Permissions**: Specific actions (CREATE, READ, UPDATE, DELETE)
- **Role-based Access**: Admin roles with appropriate permissions

### Input Validation

- **Vine Validators**: Comprehensive input validation using Vine
- **UUID Validation**: All IDs must be valid UUIDs
- **Business Logic Validation**: Product availability, bundle validity
- **SQL Injection Protection**: Lucid ORM provides automatic protection

### Security Best Practices

```javascript
// Always validate admin permissions
const hasPermission = await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.CART)

// Sanitize input data
const payload = await request.validateUsing(adminAddItemValidator)

// Log all admin actions
logger.info('Admin action', {
  adminId: auth.user.id,
  action: 'add_item',
  cartId: params.cartId,
  notes: payload.notes,
})
```

---

## 🧪 Testing & Validation

### Test Scenarios

1. **Empty Cart Operations**: Test adding items to empty carts
2. **Duplicate Items**: Verify quantity updates for existing items
3. **Bundle Management**: Test bundle creation and removal
4. **Permission Testing**: Verify role-based access control
5. **Error Handling**: Test with invalid IDs and edge cases
6. **Logic Consistency**: Ensure behavior matches app APIs

### Sample Test Code

```javascript
// Test adding item to cart
test('should add item to cart with same logic as app API', async () => {
  const response = await client
    .post(`/admin/carts/${cartId}/items`)
    .json({
      variantId: 'test-variant-id',
      quantity: 2,
      notes: 'Admin test',
    })
    .header('Authorization', `Bearer ${adminToken}`)

  response.assertStatus(201)
  response.assertBodyContains({
    success: true,
    message: 'Item added to cart successfully',
  })
})

// Test duplicate item merging
test('should merge quantities for duplicate items', async () => {
  // Add item first time
  await addItem(cartId, variantId, 2)

  // Add same item again
  const response = await addItem(cartId, variantId, 3)

  // Should have total quantity of 5
  response.assertBodyContains({
    data: { quantity: 5 },
  })
})
```

### Validation Checklist

- [ ] All required fields are validated
- [ ] Quantity values are positive integers
- [ ] Price values are positive numbers
- [ ] IDs reference valid entities
- [ ] Admin permissions are enforced
- [ ] Audit trail is properly recorded
- [ ] Logic matches app API behavior 100%

---

## 🚀 Performance Optimization

### Optimization Features

- **Concurrent Operations**: All database operations use `Promise.all()` for maximum performance
- **Smart Preloading**: Efficient relationship loading to minimize database queries
- **Bundle Intelligence**: Optimized bundle matching with efficient algorithms
- **Caching Strategy**: Implement caching for frequently accessed carts

### Best Practices

1. **Use Bulk Operations**: For multiple modifications, group operations together
2. **Limit Preloading**: Only preload relationships you actually need
3. **Batch Requests**: Group related operations in single requests
4. **Monitor Performance**: Track response times for large carts
5. **Implement Caching**: Cache cart data for frequently accessed carts

```javascript
// Example: Efficient cart loading with selective preloading
const cart = await ZnCart.query()
  .where('id', cartId)
  .preload('cartSections', (sectionQuery) => {
    sectionQuery
      .preload('cartItems', (itemQuery) => {
        itemQuery.preload('product').preload('variant')
      })
      .preload('bundle')
  })
  .first()
```

---

## 📞 Support & Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure admin has CART resource permissions
2. **Validation Errors**: Check request body format and required fields
3. **Performance Issues**: Use bulk operations for large carts
4. **Logic Inconsistency**: Verify using ShopCartService for core operations

### Getting Help

- Check API response messages for specific error details
- Verify admin permissions and role assignments
- Review audit trail for operation history
- Monitor performance metrics for optimization opportunities
- Use test file: `tests/admin/cart/admin_cart_api.test.ts`

### Debug Commands

```bash
# Run tests
node ace test tests/admin/cart/admin_cart_api.test.ts

# Check logs
tail -f logs/app.log | grep "Admin cart"

# Validate permissions
node ace bouncer:check admin-user-id CART CREATE
```

---

## 📈 Version History & Roadmap

### Current Version: v1.0

- ✅ Full cart management capabilities
- ✅ 100% logic consistency with app APIs
- ✅ Complete audit trail system
- ✅ Role-based access control
- ✅ Comprehensive error handling
- ✅ Performance optimizations

### Future Enhancements

- 🔄 Bulk operations for multiple carts
- 📊 Advanced analytics and reporting
- 🔔 Real-time notifications for cart changes
- 🎯 Advanced bundle recommendation engine
- 📱 Mobile-optimized responses

---

## 🎯 Conclusion

The Admin Cart Management API provides a complete, secure, and performant solution for administrative cart operations. With 100% logic consistency with app APIs, comprehensive audit trails, and robust error handling, it enables administrators to efficiently manage user shopping carts while maintaining data integrity and security.

**Key Benefits:**

- **Seamless Integration**: Drop-in replacement with existing cart logic
- **Complete Control**: Full CRUD operations on carts, sections, and items
- **Audit Compliance**: Detailed tracking of all administrative actions
- **Performance Optimized**: Efficient database operations and caching
- **Security First**: Role-based access control and input validation

Start integrating today with the quick start guide above, and refer to the detailed API documentation for advanced use cases.

```

```
