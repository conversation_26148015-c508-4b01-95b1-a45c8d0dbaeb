import vine from '@vinejs/vine'

/**
 * Validator for creating a new customer
 */
export const createCustomerValidator = vine.compile(
  vine.object({
    firstName: vine.string().trim().minLength(1).maxLength(255).optional(),
    lastName: vine.string().trim().minLength(1).maxLength(255).optional(),
    email: vine.string().trim().email().optional(),
    phone: vine.string().trim().minLength(10).maxLength(15).optional(),
    birthday: vine.string().optional(),
    gender: vine.string().trim().optional(),
    avatarId: vine.string().trim().uuid().optional(),
    storeId: vine.string().trim().uuid().optional(),
    address: vine
      .object({
        name: vine.string().trim().optional(),
        address1: vine.string().trim().optional(),
        address2: vine.string().trim().optional(),
        city: vine.string().trim().optional(),
        province: vine.string().trim().optional(),
        country: vine.string().trim().optional(),
        zip: vine.string().trim().optional(),
        phone: vine.string().trim().optional(),
        provinceCode: vine.string().trim().optional(),
        countryCode: vine.string().trim().optional(),
      })
      .optional(),
  })
)

/**
 * Validator for updating a customer
 */
export const updateCustomerValidator = (customerId?: string) =>
  vine.compile(
    vine.object({
      firstName: vine.string().trim().minLength(1).maxLength(255).optional(),
      lastName: vine.string().trim().minLength(1).maxLength(255).optional(),
      email: vine
        .string()
        .trim()
        .email()
        .unique(async (query, field) => {
          const user = await query
            .from('zn_users')
            .where('email', field)
            .andWhereNot('id', customerId || (null as any))
            .first()

          if (user) {
            if (!user.userId) return true
            if (user.userId === customerId && !customerId) return false
            return false
          }
          return true
        })
        .optional(),
      phone: vine
        .string()
        .trim()
        .minLength(10)
        .maxLength(15)
        .unique(async (query, field) => {
          const user = await query
            .from('zn_users')
            .where('phone', field)
            .andWhereNot('id', customerId || (null as any))
            .first()

          if (user) {
            if (!user.userId) return true
            if (user.userId === customerId && !customerId) return false
            return false
          }
          return true
        })
        .optional(),
      birthday: vine.string().optional(),
      gender: vine.string().trim().optional(),
      avatarId: vine.string().trim().uuid().optional(),
      address: vine
        .object({
          name: vine.string().trim().optional(),
          address1: vine.string().trim().optional(),
          address2: vine.string().trim().optional(),
          city: vine.string().trim().optional(),
          province: vine.string().trim().optional(),
          country: vine.string().trim().optional(),
          zip: vine.string().trim().optional(),
          phone: vine.string().trim().optional(),
          provinceCode: vine.string().trim().optional(),
          countryCode: vine.string().trim().optional(),
        })
        .optional(),
    })
  )
